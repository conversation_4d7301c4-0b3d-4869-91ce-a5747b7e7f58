using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 泛型事件对象池
    /// 相比原来的EventPool，提供更好的类型安全性和性能
    /// </summary>
    public class GenericEventPool<T> : IDisposable where T : IEvent, new()
    {
        private readonly ConcurrentQueue<T> _pool = new ConcurrentQueue<T>();
        private readonly int _maxPoolSize;
        private readonly bool _enableDebugLogging;
        
        // 统计信息
        private long _totalGets = 0;
        private long _totalReturns = 0;
        private long _totalCreated = 0;
        private long _totalPoolHits = 0;
        
        public GenericEventPool(int maxPoolSize = 100, bool enableDebugLogging = false)
        {
            _maxPoolSize = maxPoolSize;
            _enableDebugLogging = enableDebugLogging;
        }
        
        /// <summary>
        /// 从池中获取事件对象
        /// </summary>
        /// <returns>事件对象</returns>
        public T Get()
        {
            System.Threading.Interlocked.Increment(ref _totalGets);
            
            if (_pool.TryDequeue(out T eventObj))
            {
                System.Threading.Interlocked.Increment(ref _totalPoolHits);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"GenericEventPool<{typeof(T).Name}>: 从池中获取事件，池中剩余: {_pool.Count}");
                }
                
                // 重置事件状态
                if (eventObj is EventBase eventBase)
                {
                    eventBase.Reset();
                }
                
                return eventObj;
            }
            
            // 池中没有对象，创建新的
            eventObj = new T();
            System.Threading.Interlocked.Increment(ref _totalCreated);
            
            if (_enableDebugLogging)
            {
                Debug.Log($"GenericEventPool<{typeof(T).Name}>: 创建新事件对象");
            }
            
            return eventObj;
        }
        
        /// <summary>
        /// 将事件对象返回到池中
        /// </summary>
        /// <param name="eventData">事件对象</param>
        public void Return(T eventData)
        {
            if (eventData == null) return;
            
            System.Threading.Interlocked.Increment(ref _totalReturns);
            
            if (_pool.Count < _maxPoolSize)
            {
                // 清理事件状态
                eventData.IsHandled = false;
                if (eventData is EventBase eventBase)
                {
                    eventBase.Reset();
                }
                
                _pool.Enqueue(eventData);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"GenericEventPool<{typeof(T).Name}>: 返回事件到池，池中数量: {_pool.Count}");
                }
            }
            else if (_enableDebugLogging)
            {
                Debug.Log($"GenericEventPool<{typeof(T).Name}>: 池已满，丢弃事件");
            }
        }
        
        /// <summary>
        /// 清空池
        /// </summary>
        public void Clear()
        {
            int count = _pool.Count;
            
            while (_pool.TryDequeue(out _))
            {
                // 清空队列
            }
            
            if (_enableDebugLogging)
            {
                Debug.Log($"GenericEventPool<{typeof(T).Name}>: 清空池，清理了 {count} 个对象");
            }
        }
        
        /// <summary>
        /// 获取池统计信息
        /// </summary>
        /// <returns>池统计信息</returns>
        public GenericEventPoolStatistics GetStatistics()
        {
            return new GenericEventPoolStatistics
            {
                EventType = typeof(T),
                TotalGets = _totalGets,
                TotalReturns = _totalReturns,
                TotalCreated = _totalCreated,
                TotalPoolHits = _totalPoolHits,
                PoolHitRate = _totalGets > 0 ? (double)_totalPoolHits / _totalGets : 0,
                CurrentPoolSize = _pool.Count,
                MaxPoolSize = _maxPoolSize
            };
        }
        
        public void Dispose()
        {
            Clear();
        }
    }
    
    /// <summary>
    /// 泛型事件池统计信息
    /// </summary>
    public class GenericEventPoolStatistics
    {
        public Type EventType { get; set; }
        public long TotalGets { get; set; }
        public long TotalReturns { get; set; }
        public long TotalCreated { get; set; }
        public long TotalPoolHits { get; set; }
        public double PoolHitRate { get; set; }
        public int CurrentPoolSize { get; set; }
        public int MaxPoolSize { get; set; }
        
        public override string ToString()
        {
            return $"EventPool<{EventType.Name}>: Gets={TotalGets}, Returns={TotalReturns}, " +
                   $"Created={TotalCreated}, HitRate={PoolHitRate:P2}, Current={CurrentPoolSize}/{MaxPoolSize}";
        }
    }
    
    /// <summary>
    /// 事件池管理器
    /// 管理多个不同类型的事件池
    /// </summary>
    public class EventPoolManager : IDisposable
    {
        private readonly ConcurrentDictionary<Type, object> _pools = new ConcurrentDictionary<Type, object>();
        private readonly int _defaultMaxPoolSize;
        private readonly bool _enableDebugLogging;
        
        public EventPoolManager(int defaultMaxPoolSize = 100, bool enableDebugLogging = false)
        {
            _defaultMaxPoolSize = defaultMaxPoolSize;
            _enableDebugLogging = enableDebugLogging;
        }
        
        /// <summary>
        /// 获取指定类型的事件池
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>事件池</returns>
        public GenericEventPool<T> GetPool<T>() where T : IEvent, new()
        {
            var pool = _pools.GetOrAdd(typeof(T), _ => new GenericEventPool<T>(_defaultMaxPoolSize, _enableDebugLogging));
            return (GenericEventPool<T>)pool;
        }
        
        /// <summary>
        /// 从池中获取事件对象
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>事件对象</returns>
        public T Get<T>() where T : IEvent, new()
        {
            return GetPool<T>().Get();
        }
        
        /// <summary>
        /// 将事件对象返回到池中
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件对象</param>
        public void Return<T>(T eventData) where T : IEvent, new()
        {
            if (eventData != null)
            {
                GetPool<T>().Return(eventData);
            }
        }
        
        /// <summary>
        /// 清空指定类型的池
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void ClearPool<T>() where T : IEvent, new()
        {
            if (_pools.TryGetValue(typeof(T), out var pool))
            {
                ((GenericEventPool<T>)pool).Clear();
            }
        }
        
        /// <summary>
        /// 清空所有池
        /// </summary>
        public void ClearAllPools()
        {
            foreach (var pool in _pools.Values)
            {
                if (pool is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            _pools.Clear();
        }
        
        /// <summary>
        /// 获取所有池的统计信息
        /// </summary>
        /// <returns>统计信息列表</returns>
        public List<GenericEventPoolStatistics> GetAllStatistics()
        {
            var statistics = new List<GenericEventPoolStatistics>();
            
            foreach (var kvp in _pools)
            {
                if (kvp.Value is IDisposable pool)
                {
                    var method = pool.GetType().GetMethod("GetStatistics");
                    if (method != null)
                    {
                        var stats = method.Invoke(pool, null) as GenericEventPoolStatistics;
                        if (stats != null)
                        {
                            statistics.Add(stats);
                        }
                    }
                }
            }
            
            return statistics;
        }
        
        public void Dispose()
        {
            ClearAllPools();
        }
    }
}