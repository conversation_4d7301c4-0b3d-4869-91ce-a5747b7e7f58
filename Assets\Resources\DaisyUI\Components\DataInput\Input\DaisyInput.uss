/* DaisyUI Inputs - 输入框组件样式 */

/* 基础输入框样式 */
.daisy-input {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* 输入框标签 */
.daisy-input-label {
    font-size: 14px;
    -unity-font-style: normal;
    color: var(--base-content);
    margin-bottom: 4px;
}

/* 输入框容器 */
.daisy-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

/* 输入框字段 */
.daisy-input-field {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--base-content);
    background-color: var(--base-100);
    border-width: 1px;
    border-radius: var(--border-radius);
    transition: all 0.2s ease-in-out;
}

/* 输入框字段内部元素 */
.daisy-input-field > .unity-text-field__input {
    background-color: transparent;
    border-width: 0;
    padding: 0;
    margin: 0;
}

/* 焦点状态 */
.daisy-input-focused .daisy-input-field {
    border-color: var(--primary);
}

/* 帮助文本 */
.daisy-input-helper {
    font-size: 12px;
    color: var(--base-content);
    opacity: 0.7;
    margin-top: 4px;
}

/* 输入框尺寸变体 */
.daisy-input-xs .daisy-input-field {
    padding: 6px 8px;
    font-size: 12px;
}

.daisy-input-xs .daisy-input-label {
    font-size: 12px;
}

.daisy-input-sm .daisy-input-field {
    padding: 8px 12px;
    font-size: 13px;
}

.daisy-input-sm .daisy-input-label {
    font-size: 13px;
}

.daisy-input-lg .daisy-input-field {
    padding: 16px 20px;
    font-size: 16px;
}

.daisy-input-lg .daisy-input-label {
    font-size: 16px;
}

.daisy-input-xl .daisy-input-field {
    padding: 20px 24px;
    font-size: 18px;
}

.daisy-input-xl .daisy-input-label {
    font-size: 18px;
}


/* 输入框修饰符 */

/* 边框样式 */
.daisy-input-bordered .daisy-input-field {
    border-color: var(--base-300);
    background-color: var(--base-100);
}

.daisy-input-bordered.daisy-input-focused .daisy-input-field {
    border-color: var(--primary);
}

/* 幽灵样式 */
.daisy-input-ghost .daisy-input-field {
    border-color: transparent;
    background-color: transparent;
}

.daisy-input-ghost.daisy-input-focused .daisy-input-field {
    border-color: var(--primary);
    background-color: var(--base-100);
}

/* 颜色变体样式 */

/* 中性色变体 */
.daisy-input-neutral .daisy-input-field {
    border-color: var(--neutral);
}

.daisy-input-neutral.daisy-input-focused .daisy-input-field {
    border-color: var(--neutral);
}

/* 主要色变体 */
.daisy-input-primary .daisy-input-field {
    border-color: var(--primary);
}

.daisy-input-primary.daisy-input-focused .daisy-input-field {
    border-color: var(--primary);
}

/* 次要色变体 */
.daisy-input-secondary .daisy-input-field {
    border-color: var(--secondary);
}

.daisy-input-secondary.daisy-input-focused .daisy-input-field {
    border-color: var(--secondary);
}

/* 强调色变体 */
.daisy-input-accent .daisy-input-field {
    border-color: var(--accent);
}

.daisy-input-accent.daisy-input-focused .daisy-input-field {
    border-color: var(--accent);
}

/* 状态样式 */

/* 错误状态 */
.daisy-input-error .daisy-input-field {
    border-color: var(--error);
}

.daisy-input-error.daisy-input-focused .daisy-input-field {
    border-color: var(--error);
}

.daisy-input-error .daisy-input-helper {
    color: var(--error);
}

/* 成功状态 */
.daisy-input-success .daisy-input-field {
    border-color: var(--success);
}

.daisy-input-success.daisy-input-focused .daisy-input-field {
    border-color: var(--success);
}

.daisy-input-success .daisy-input-helper {
    color: var(--success);
}

/* 警告状态 */
.daisy-input-warning .daisy-input-field {
    border-color: var(--warning);
}

.daisy-input-warning.daisy-input-focused .daisy-input-field {
    border-color: var(--warning);
}

.daisy-input-warning .daisy-input-helper {
    color: var(--warning);
}

/* 信息状态 */
.daisy-input-info .daisy-input-field {
    border-color: var(--info);
}

.daisy-input-info.daisy-input-focused .daisy-input-field {
    border-color: var(--info);
}

.daisy-input-info .daisy-input-helper {
    color: var(--info);
}

/* 禁用状态 */
.daisy-input-disabled .daisy-input-field {
    opacity: 0.6;
    background-color: var(--base-200);
    border-color: var(--base-300);
}

.daisy-input-disabled .daisy-input-label {
    opacity: 0.6;
}

/* 只读状态 */
.daisy-input-readonly .daisy-input-field {
    background-color: var(--base-200);
}

/* 增长修饰符 */
.daisy-input-grow {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: auto;
}

/* 连接项修饰符 */
.daisy-input-join-item {
    border-radius: 0;
}


/* 输入类型特定样式 */

/* 密码输入框 */
.daisy-input-password .daisy-input-field {
}

/* 数字输入框 */
.daisy-input-number .daisy-input-field {
    -unity-text-align: middle-right;
}

/* 邮箱输入框 */
.daisy-input-email .daisy-input-field {
}

/* 悬停效果 */
.daisy-input:hover .daisy-input-field {
    border-color: var(--primary);
}

/* 输入框组合 */
.daisy-input-group {
    display: flex;
    align-items: flex-end;
}

.daisy-input-group .daisy-input {
    flex: 1;
}

/* 输入框网格 */
.daisy-input-grid {
    display: flex;
    flex-direction: column;
}

/* 暗色主题适配 */
.theme-dark .daisy-input-field {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
    --unity-cursor-color: rgb(210, 210, 210);
}

.theme-dark .daisy-input-field:focus,
.theme-dark .daisy-input-focused .daisy-input-field {
    border-color: rgb(58, 121, 187);
}

.theme-dark .daisy-input:hover .daisy-input-field {
    border-color: rgb(58, 121, 187);
}

.theme-dark .daisy-input-ghost .daisy-input-field {
    background-color: transparent;
}

.theme-dark .daisy-input-disabled .daisy-input-field {
    background-color: rgb(32, 32, 32);
    border-color: rgb(24, 24, 24);
    opacity: 0.6;
}

.theme-dark .daisy-input-readonly .daisy-input-field {
    background-color: rgb(32, 32, 32);
    border-color: rgb(24, 24, 24);
}

.theme-dark .daisy-input-label {
    color: rgb(190, 190, 190);
}

.theme-dark .daisy-input-helper {
    color: rgb(170, 170, 170);
}





