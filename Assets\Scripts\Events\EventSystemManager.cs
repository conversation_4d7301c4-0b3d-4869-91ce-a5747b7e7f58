using System;
using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Management;
using BlastingDesign.Events.Services;
using BlastingDesign.Events.Handlers;
using BlastingDesign.Events.Compatibility;
using BlastingDesign.UI.Core;
using BlastingDesign.Core.Input;

namespace BlastingDesign.Events
{
    /// <summary>
    /// 事件系统管理器
    /// 统一管理和初始化整个新事件系统
    /// </summary>
    public class EventSystemManager : MonoBehaviour
    {
        [Header("事件系统配置")]
        [SerializeField] private bool useObjectPool = true;
        [SerializeField] private bool enableDebugMode = false;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool enableEventDebugging = true;
        [SerializeField] private bool enableEventReplay = true;

        [Header("兼容性设置")]
        [SerializeField] private bool enableLegacyCompatibility = true;
        [SerializeField] private UIEventSystem legacyEventSystem;

        [Header("输入系统集成")]
        [SerializeField] private bool enableInputIntegration = true;
        [SerializeField] private InputEventPriorityManager inputPriorityManager;

        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = true;
        [SerializeField] private bool enableDetailedLogging = false;

        // 核心组件
        private EventBus _eventBus;
        private EventTypeRegistry _eventTypeRegistry;
        private EventFilterManager _filterManager;

        // 服务组件
        private EventPerformanceMonitor _performanceMonitor;
        private EventDebugger _eventDebugger;
        private EventReplaySystem _replaySystem;

        // 处理器
        private InputEventHandler _inputEventHandler;

        // 兼容性
        private UIEventSystemAdapter _compatibilityAdapter;

        // 单例
        public static EventSystemManager Instance { get; private set; }

        // 公共接口
        public IEventBus EventBus => _compatibilityAdapter ?? (IEventBus)_eventBus;
        public EventTypeRegistry EventTypeRegistry => _eventTypeRegistry;
        public EventFilterManager FilterManager => _filterManager;
        public EventPerformanceMonitor PerformanceMonitor => _performanceMonitor;
        public EventDebugger EventDebugger => _eventDebugger;
        public EventReplaySystem ReplaySystem => _replaySystem;

        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);

                // 验证配置
                if (ValidateConfiguration())
                {
                    InitializeEventSystem();
                }
                else
                {
                    Debug.LogError("EventSystemManager: 配置验证失败，事件系统初始化已跳过");
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 验证EventSystemManager的配置
        /// </summary>
        /// <returns>配置是否有效</returns>
        private bool ValidateConfiguration()
        {
            bool isValid = true;

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 开始配置验证");
            }

            // 检查兼容性配置
            if (enableLegacyCompatibility && legacyEventSystem == null)
            {
                Debug.LogWarning("EventSystemManager: 兼容性模式已启用但UIEventSystem未分配。将自动查找UIEventSystem实例。");
                legacyEventSystem = FindFirstObjectByType<UIEventSystem>();

                if (legacyEventSystem == null)
                {
                    Debug.LogWarning("EventSystemManager: 未找到UIEventSystem实例，兼容性模式将被禁用");
                    enableLegacyCompatibility = false;
                }
            }

            // 检查输入系统配置
            if (enableInputIntegration && inputPriorityManager == null)
            {
                Debug.LogWarning("EventSystemManager: 输入集成已启用但InputPriorityManager未分配。将自动查找InputPriorityManager实例。");
                inputPriorityManager = FindFirstObjectByType<InputEventPriorityManager>();

                if (inputPriorityManager == null)
                {
                    Debug.LogWarning("EventSystemManager: 未找到InputPriorityManager实例，输入集成将被禁用");
                    enableInputIntegration = false;
                }
            }

            if (showDebugInfo)
            {
                Debug.Log($"EventSystemManager: 配置验证完成 - 兼容性模式: {enableLegacyCompatibility}, 输入集成: {enableInputIntegration}");
            }

            return isValid;
        }

        private void InitializeEventSystem()
        {
            try
            {
                if (showDebugInfo)
                {
                    Debug.Log("EventSystemManager: 开始初始化新事件系统");
                }

                // 1. 初始化核心组件
                InitializeCoreComponents();

                // 2. 初始化管理组件
                InitializeManagementComponents();

                // 3. 初始化服务组件
                InitializeServiceComponents();

                // 4. 初始化处理器
                InitializeHandlers();

                // 5. 设置兼容性适配器
                SetupCompatibilityAdapter();

                // 6. 集成调试和监控
                IntegrateDebuggingAndMonitoring();

                if (showDebugInfo)
                {
                    Debug.Log("EventSystemManager: 新事件系统初始化完成");
                    // LogSystemStatistics();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"EventSystemManager: 初始化事件系统时发生错误: {ex.Message}");
            }
        }

        private void InitializeCoreComponents()
        {
            // 创建事件总线
            _eventBus = new EventBus(
                useObjectPool: useObjectPool,
                debugMode: enableDebugMode,
                enablePerformanceMonitoring: enablePerformanceMonitoring
            );

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 事件总线初始化完成");
            }
        }

        private void InitializeManagementComponents()
        {
            // 创建事件类型注册表
            _eventTypeRegistry = new EventTypeRegistry(
                enableAutoDiscovery: true,
                enableDebugLogging: enableDetailedLogging
            );

            // 创建事件过滤管理器
            _filterManager = new EventFilterManager(
                enableDebugLogging: enableDetailedLogging,
                enableFilterChaining: true
            );

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 管理组件初始化完成");
            }
        }

        private void InitializeServiceComponents()
        {
            // 创建性能监控器
            if (enablePerformanceMonitoring)
            {
                _performanceMonitor = _eventBus.GetPerformanceMonitor();
            }

            // 创建事件调试器
            if (enableEventDebugging)
            {
                _eventDebugger = new EventDebugger(
                    maxTraceRecords: 1000,
                    enableDetailedLogging: enableDetailedLogging,
                    trackEventSources: true
                );
            }

            // 创建事件重放系统
            if (enableEventReplay)
            {
                _replaySystem = new EventReplaySystem(
                    _eventBus,
                    maxRecordedEvents: 10000,
                    enableDebugLogging: enableDetailedLogging
                );
            }

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 服务组件初始化完成");
            }
        }

        private void InitializeHandlers()
        {
            // 创建输入事件处理器
            if (enableInputIntegration)
            {
                if (inputPriorityManager != null)
                {
                    try
                    {
                        _inputEventHandler = new InputEventHandler(
                            _eventBus,
                            inputPriorityManager,
                            _filterManager,
                            enableDetailedLogging
                        );

                        if (showDebugInfo)
                        {
                            Debug.Log("EventSystemManager: 输入事件处理器初始化完成");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventSystemManager: 输入事件处理器初始化失败: {ex.Message}");
                        enableInputIntegration = false; // 禁用输入集成以避免后续错误
                    }
                }
                else
                {
                    Debug.LogWarning("EventSystemManager: 输入集成已启用但InputPriorityManager未分配，跳过输入事件处理器初始化");
                    enableInputIntegration = false;
                }
            }

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 事件处理器初始化完成");
            }
        }

        private void SetupCompatibilityAdapter()
        {
            if (enableLegacyCompatibility)
            {
                if (legacyEventSystem != null)
                {
                    try
                    {
                        _compatibilityAdapter = new UIEventSystemAdapter(
                            legacyEventSystem,
                            _eventBus,
                            enableDetailedLogging
                        );

                        // 初始化兼容性支持
                        UIEventSystemCompat.Initialize(_compatibilityAdapter);

                        if (showDebugInfo)
                        {
                            Debug.Log("EventSystemManager: 兼容性适配器设置完成");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventSystemManager: 兼容性适配器设置失败: {ex.Message}");
                        enableLegacyCompatibility = false; // 禁用兼容性以避免后续错误
                    }
                }
                else
                {
                    Debug.LogWarning("EventSystemManager: 兼容性模式已启用但UIEventSystem未分配，跳过兼容性适配器设置");
                    enableLegacyCompatibility = false;
                }
            }
        }

        private void IntegrateDebuggingAndMonitoring()
        {
            // 集成调试器到事件总线
            if (_eventDebugger != null)
            {
                // 这里应该将调试器集成到事件发布流程中
                // 由于EventBus没有直接的钩子，这里需要通过其他方式实现
                if (showDebugInfo)
                {
                    Debug.Log("EventSystemManager: 调试器集成完成");
                }
            }

            // 集成重放系统
            if (_replaySystem != null)
            {
                // 类似地，需要将重放系统集成到事件流程中
                if (showDebugInfo)
                {
                    Debug.Log("EventSystemManager: 重放系统集成完成");
                }
            }
        }

        private void LogSystemStatistics()
        {
            var stats = _eventBus.GetStatistics();
            var registryStats = _eventTypeRegistry.GetStatistics();

            Debug.Log($"事件系统统计信息:");
            Debug.Log($"- 总发布事件: {stats.TotalPublishedEvents}");
            Debug.Log($"- 总订阅数: {stats.TotalSubscriptions}");
            Debug.Log($"- 活跃事件类型: {stats.ActiveEventTypes}");
            Debug.Log($"- 注册事件类型: {registryStats.TotalRegisteredTypes}");
            Debug.Log($"- 内置事件类型: {registryStats.BuiltInEventTypes}");
            Debug.Log($"- 自定义事件类型: {registryStats.CustomEventTypes}");

            if (_performanceMonitor != null)
            {
                var perfStats = _performanceMonitor.GetOverallStatistics();
                Debug.Log($"- 性能监控: 处理了 {perfStats.TotalEventsProcessed} 个事件，平均处理时间 {perfStats.AverageProcessingTime:F2}ms");
            }
        }

        private void Update()
        {
            // 更新事件调度器
            _eventBus?.Update();
        }

        private void OnDestroy()
        {
            // 清理资源
            _inputEventHandler?.Dispose();
            _replaySystem?.Dispose();
            _eventDebugger?.Dispose();
            _filterManager?.Dispose();
            _eventTypeRegistry?.Dispose();
            _compatibilityAdapter?.Dispose();
            _eventBus?.Dispose();

            if (showDebugInfo)
            {
                Debug.Log("EventSystemManager: 事件系统资源已清理");
            }
        }

        #region 公共API方法

        /// <summary>
        /// 发布事件的便捷方法
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        public void PublishEvent<T>(T eventData) where T : IEvent
        {
            EventBus?.Publish(eventData);
        }

        /// <summary>
        /// 订阅事件的便捷方法
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        public IEventSubscription SubscribeEvent<T>(Action<T> handler) where T : IEvent
        {
            return EventBus?.Subscribe(handler);
        }

        /// <summary>
        /// 生成完整的系统报告
        /// </summary>
        /// <returns>系统报告字符串</returns>
        public string GenerateSystemReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 事件系统完整报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // 基础统计
            var stats = _eventBus.GetStatistics();
            report.AppendLine("=== 基础统计 ===");
            report.AppendLine($"总发布事件: {stats.TotalPublishedEvents}");
            report.AppendLine($"总订阅数: {stats.TotalSubscriptions}");
            report.AppendLine($"活跃事件类型: {stats.ActiveEventTypes}");
            report.AppendLine();

            // 性能报告
            if (_performanceMonitor != null)
            {
                report.AppendLine(_eventBus.GeneratePerformanceReport());
                report.AppendLine();
            }

            // 调试报告
            if (_eventDebugger != null)
            {
                report.AppendLine(_eventDebugger.GenerateEventFlowReport());
                report.AppendLine();
            }

            // 重放统计
            if (_replaySystem != null)
            {
                report.AppendLine(_replaySystem.GenerateReplayStatistics());
            }

            return report.ToString();
        }

        #endregion
    }
}
