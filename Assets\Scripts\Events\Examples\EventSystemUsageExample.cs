using System;
using UnityEngine;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Compatibility;

namespace BlastingDesign.Events.Examples
{
    /// <summary>
    /// 新事件系统使用示例
    /// 展示如何使用新事件系统的各种功能
    /// </summary>
    public class EventSystemUsageExample : MonoBehaviour
    {
        private IEventSubscription _menuSubscription;
        private IEventSubscription _toolSubscription;
        private IEventSubscription _statusSubscription;
        
        private void Start()
        {
            // 等待事件系统初始化
            if (EventSystemManager.Instance == null)
            {
                Debug.LogError("EventSystemUsageExample: EventSystemManager未初始化");
                return;
            }
            
            SetupEventSubscriptions();
            DemonstrateBasicUsage();
            DemonstrateAdvancedFeatures();
            DemonstrateLegacyCompatibility();
        }
        
        private void SetupEventSubscriptions()
        {
            var eventBus = EventSystemManager.Instance.EventBus;
            
            // 基础事件订阅
            _menuSubscription = eventBus.Subscribe<MenuItemClickedEvent>(OnMenuItemClicked);
            _toolSubscription = eventBus.Subscribe<ToolSelectedEvent>(OnToolSelected);
            _statusSubscription = eventBus.Subscribe<StatusMessageEvent>(OnStatusMessage);
            
            Debug.Log("EventSystemUsageExample: 事件订阅设置完成");
        }
        
        private void DemonstrateBasicUsage()
        {
            var eventBus = EventSystemManager.Instance.EventBus;
            
            Debug.Log("=== 基础使用示例 ===");
            
            // 发布菜单点击事件
            eventBus.Publish(new MenuItemClickedEvent("File.New"));
            eventBus.Publish(new MenuItemClickedEvent("Edit.Copy"));
            
            // 发布工具选择事件
            eventBus.Publish(new ToolSelectedEvent("SelectTool"));
            eventBus.Publish(new ToolSelectedEvent("MoveTool"));
            
            // 发布状态消息事件
            eventBus.Publish(new StatusMessageEvent("操作完成", StatusMessageType.Success));
        }
        
        private void DemonstrateAdvancedFeatures()
        {
            var eventBus = EventSystemManager.Instance.EventBus;
            var filterManager = EventSystemManager.Instance.FilterManager;
            
            Debug.Log("=== 高级功能示例 ===");
            
            // 条件订阅示例
            var conditionalSubscription = eventBus.Subscribe<MenuItemClickedEvent>(
                OnImportantMenuItemClicked,
                evt => evt.MenuItem.StartsWith("File.") // 只处理File菜单的事件
            );
            
            // 优先级订阅示例
            var highPrioritySubscription = eventBus.Subscribe<StatusMessageEvent>(
                OnHighPriorityStatus,
                100 // 高优先级
            );
            
            // 添加全局过滤器
            filterManager.AddFilter<MenuItemClickedEvent>(
                evt => !evt.MenuItem.Contains("Debug"), // 过滤掉Debug相关的菜单项
                50,
                "DebugMenuFilter"
            );
            
            // 测试过滤器
            eventBus.Publish(new MenuItemClickedEvent("File.Open")); // 应该通过
            eventBus.Publish(new MenuItemClickedEvent("Debug.ShowConsole")); // 应该被过滤
            
            // 延迟发布示例
            eventBus.PublishDelayed(new StatusMessageEvent("延迟消息", StatusMessageType.Info), 2.0f);
            
            // 批量发布示例
            var batchEvents = new[]
            {
                new StatusMessageEvent("批量消息1", StatusMessageType.Info),
                new StatusMessageEvent("批量消息2", StatusMessageType.Info),
                new StatusMessageEvent("批量消息3", StatusMessageType.Info)
            };
            eventBus.PublishBatch(batchEvents);
            
            // 清理订阅
            conditionalSubscription?.Dispose();
            highPrioritySubscription?.Dispose();
        }
        
        private void DemonstrateLegacyCompatibility()
        {
            Debug.Log("=== 兼容性示例 ===");
            
            // 使用兼容性API（已过时，但仍然可用）
            UIEventSystemCompat.TriggerMenuItemClicked("Help.About");
            UIEventSystemCompat.TriggerToolSelected("RotateTool");
            UIEventSystemCompat.TriggerStatusMessage("兼容性测试消息");
            
            // 兼容性订阅示例
            var compatSubscription = UIEventSystemCompat.SubscribeMenuItemClicked(menuItem =>
            {
                Debug.Log($"兼容性订阅收到菜单点击: {menuItem}");
            });
            
            // 测试兼容性
            UIEventSystemCompat.TriggerMenuItemClicked("View.Properties");
            
            // 清理兼容性订阅
            compatSubscription?.Dispose();
        }
        
        #region 事件处理器
        
        private void OnMenuItemClicked(MenuItemClickedEvent evt)
        {
            Debug.Log($"菜单项点击: {evt.MenuItem} (来源: {evt.Source})");
            
            // 根据菜单项执行不同操作
            switch (evt.MenuItem)
            {
                case "File.New":
                    Debug.Log("执行新建文件操作");
                    break;
                case "File.Open":
                    Debug.Log("执行打开文件操作");
                    break;
                case "Edit.Copy":
                    Debug.Log("执行复制操作");
                    break;
                default:
                    Debug.Log($"未处理的菜单项: {evt.MenuItem}");
                    break;
            }
        }
        
        private void OnToolSelected(ToolSelectedEvent evt)
        {
            Debug.Log($"工具选择: {evt.ToolName} (来源: {evt.Source})");
            
            // 根据工具类型执行不同操作
            switch (evt.ToolName)
            {
                case "SelectTool":
                    Debug.Log("切换到选择工具");
                    break;
                case "MoveTool":
                    Debug.Log("切换到移动工具");
                    break;
                case "RotateTool":
                    Debug.Log("切换到旋转工具");
                    break;
                default:
                    Debug.Log($"未知工具: {evt.ToolName}");
                    break;
            }
        }
        
        private void OnStatusMessage(StatusMessageEvent evt)
        {
            string typeStr = evt.MessageType switch
            {
                StatusMessageType.Info => "信息",
                StatusMessageType.Warning => "警告",
                StatusMessageType.Error => "错误",
                StatusMessageType.Success => "成功",
                _ => "未知"
            };
            
            Debug.Log($"状态消息 [{typeStr}]: {evt.Message} (持续时间: {evt.Duration}s)");
        }
        
        private void OnImportantMenuItemClicked(MenuItemClickedEvent evt)
        {
            Debug.Log($"重要菜单项点击 (条件订阅): {evt.MenuItem}");
        }
        
        private void OnHighPriorityStatus(StatusMessageEvent evt)
        {
            Debug.Log($"高优先级状态消息: {evt.Message}");
        }
        
        #endregion
        
        #region 调试和监控示例
        
        [ContextMenu("显示性能报告")]
        private void ShowPerformanceReport()
        {
            if (EventSystemManager.Instance?.PerformanceMonitor != null)
            {
                var report = EventSystemManager.Instance.GenerateSystemReport();
                Debug.Log(report);
            }
            else
            {
                Debug.LogWarning("性能监控未启用");
            }
        }
        
        [ContextMenu("开始事件记录")]
        private void StartEventRecording()
        {
            if (EventSystemManager.Instance?.ReplaySystem != null)
            {
                EventSystemManager.Instance.ReplaySystem.StartRecording();
                Debug.Log("开始记录事件");
            }
            else
            {
                Debug.LogWarning("事件重放系统未启用");
            }
        }
        
        [ContextMenu("停止事件记录")]
        private void StopEventRecording()
        {
            if (EventSystemManager.Instance?.ReplaySystem != null)
            {
                EventSystemManager.Instance.ReplaySystem.StopRecording();
                Debug.Log("停止记录事件");
            }
        }
        
        [ContextMenu("重放事件")]
        private void ReplayEvents()
        {
            if (EventSystemManager.Instance?.ReplaySystem != null)
            {
                EventSystemManager.Instance.ReplaySystem.StartReplay();
                Debug.Log("开始重放事件");
            }
        }
        
        [ContextMenu("显示事件类型信息")]
        private void ShowEventTypeInfo()
        {
            if (EventSystemManager.Instance?.EventTypeRegistry != null)
            {
                var stats = EventSystemManager.Instance.EventTypeRegistry.GetStatistics();
                Debug.Log($"事件类型统计: 总计 {stats.TotalRegisteredTypes} 个类型，内置 {stats.BuiltInEventTypes} 个，自定义 {stats.CustomEventTypes} 个");
                
                var categories = EventSystemManager.Instance.EventTypeRegistry.GetAllCategories();
                Debug.Log($"事件分类: {string.Join(", ", categories)}");
            }
        }
        
        #endregion
        
        private void OnDestroy()
        {
            // 清理订阅
            _menuSubscription?.Dispose();
            _toolSubscription?.Dispose();
            _statusSubscription?.Dispose();
            
            Debug.Log("EventSystemUsageExample: 清理完成");
        }
        
        #region 测试按钮（仅在编辑器中显示）
        
        #if UNITY_EDITOR
        [ContextMenu("测试所有功能")]
        private void TestAllFeatures()
        {
            DemonstrateBasicUsage();
            DemonstrateAdvancedFeatures();
            DemonstrateLegacyCompatibility();
        }
        #endif
        
        #endregion
    }
}
