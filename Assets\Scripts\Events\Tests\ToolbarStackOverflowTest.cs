using UnityEngine;
using BlastingDesign.UI.Core;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;
using System.Collections.Generic;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 工具栏堆栈溢出问题测试脚本
    /// 用于验证重入保护机制是否有效
    /// </summary>
    public class ToolbarStackOverflowTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool enableTest = false;
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private int maxTestIterations = 10;

        private List<IEventSubscription> testSubscriptions = new List<IEventSubscription>();
        private int recursionAttempts = 0;
        private bool testCompleted = false;

        private void Start()
        {
            if (enableTest)
            {
                StartStackOverflowTest();
            }
        }

        private void StartStackOverflowTest()
        {
            Logging.LogInfo("ToolbarStackOverflowTest", "开始堆栈溢出保护测试");

            // 订阅工具栏事件，在事件处理器中尝试触发递归调用
            SetupRecursiveEventHandlers();

            // 模拟触发工具选择事件
            SimulateToolSelection();
        }

        private void SetupRecursiveEventHandlers()
        {
            var eventBus = BlastingDesign.Events.EventSystemManager.Instance?.EventBus;
            if (eventBus == null)
            {
                Logging.LogError("ToolbarStackOverflowTest", "EventBus未找到，无法进行测试");
                return;
            }

            // 订阅工具状态变更事件，在处理器中尝试触发新的工具选择
            var subscription = eventBus.Subscribe<ToolStateChangedEvent>(OnToolStateChanged_Recursive);
            testSubscriptions.Add(subscription);

            // 订阅工具组切换事件
            var groupSubscription = eventBus.Subscribe<ToolGroupSwitchedEvent>(OnToolGroupSwitched_Recursive);
            testSubscriptions.Add(groupSubscription);

            Logging.LogInfo("ToolbarStackOverflowTest", "递归事件处理器设置完成");
        }

        private void OnToolStateChanged_Recursive(ToolStateChangedEvent evt)
        {
            recursionAttempts++;

            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarStackOverflowTest",
                    $"收到工具状态变更事件 (递归尝试 #{recursionAttempts}): {evt.ToolName} - {evt.PreviousState} -> {evt.CurrentState}");
            }

            // 尝试触发递归调用（这应该被重入保护阻止）
            if (recursionAttempts < maxTestIterations && !testCompleted)
            {
                TryTriggerRecursiveCall(evt.ToolName);
            }
            else if (recursionAttempts >= maxTestIterations)
            {
                CompleteTest(true);
            }
        }

        private void OnToolGroupSwitched_Recursive(ToolGroupSwitchedEvent evt)
        {
            if (enableDetailedLogging)
            {
                Logging.LogInfo("ToolbarStackOverflowTest",
                    $"收到工具组切换事件: {evt.GroupName} - {evt.PreviousToolName} -> {evt.CurrentToolName}");
            }

            // 尝试触发递归调用
            if (recursionAttempts < maxTestIterations && !testCompleted)
            {
                TryTriggerRecursiveCall(evt.CurrentToolName);
            }
        }

        private void TryTriggerRecursiveCall(string toolName)
        {
            try
            {
                // 尝试通过事件系统触发新的工具选择
                var eventBus = BlastingDesign.Events.EventSystemManager.Instance?.EventBus;
                if (eventBus != null)
                {
                    // 发布一个新的工具状态变更事件，这可能会触发递归
                    eventBus.Publish(new ToolStateChangedEvent(
                        $"test-tool-{recursionAttempts}",
                        ToolState.Active,
                        ToolState.Inactive,
                        null,
                        "StackOverflowTest"
                    ));

                    if (enableDetailedLogging)
                    {
                        Logging.LogInfo("ToolbarStackOverflowTest",
                            $"成功发布递归测试事件 #{recursionAttempts}");
                    }
                }
            }
            catch (System.StackOverflowException)
            {
                Logging.LogError("ToolbarStackOverflowTest",
                    "检测到堆栈溢出！重入保护机制失败！");
                CompleteTest(false);
            }
            catch (System.Exception ex)
            {
                Logging.LogWarning("ToolbarStackOverflowTest",
                    $"递归调用被阻止或出现其他异常: {ex.Message}");
            }
        }

        private void SimulateToolSelection()
        {
            // 模拟初始工具选择事件
            var eventBus = BlastingDesign.Events.EventSystemManager.Instance?.EventBus;
            if (eventBus != null)
            {
                eventBus.Publish(new ToolStateChangedEvent(
                    "initial-tool",
                    ToolState.Active,
                    ToolState.Inactive,
                    null,
                    "StackOverflowTest"
                ));

                Logging.LogInfo("ToolbarStackOverflowTest", "初始工具选择事件已发布");
            }
        }

        private void CompleteTest(bool success)
        {
            if (testCompleted) return;

            testCompleted = true;

            string result = success ? "成功" : "失败";
            string message = success
                ? $"堆栈溢出保护测试{result}！递归尝试次数: {recursionAttempts}，未发生堆栈溢出。"
                : $"堆栈溢出保护测试{result}！检测到堆栈溢出。";

            Logging.LogInfo("ToolbarStackOverflowTest", message);

            // 清理订阅
            CleanupSubscriptions();
        }

        private void CleanupSubscriptions()
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            var eventBus = eventSystemManager != null ? eventSystemManager.EventBus : null;

            foreach (var subscription in testSubscriptions)
            {
                if (subscription != null)
                {
                    // 优先使用EventBus的Unsubscribe方法，这样可以确保完全清理
                    if (eventBus != null)
                    {
                        eventBus.Unsubscribe(subscription);
                    }
                    else
                    {
                        // 如果EventBus不可用，直接Dispose订阅
                        subscription.Dispose();
                    }
                }
            }
            testSubscriptions.Clear();

            Logging.LogInfo("ToolbarStackOverflowTest", "测试订阅已清理");
        }

        private void OnDestroy()
        {
            CleanupSubscriptions();
        }

        [ContextMenu("开始堆栈溢出测试")]
        public void StartTestFromMenu()
        {
            if (!testCompleted)
            {
                enableTest = true;
                StartStackOverflowTest();
            }
            else
            {
                Logging.LogWarning("ToolbarStackOverflowTest", "测试已完成，请重新启动场景进行新测试");
            }
        }

        [ContextMenu("重置测试")]
        public void ResetTest()
        {
            CleanupSubscriptions();
            recursionAttempts = 0;
            testCompleted = false;
            Logging.LogInfo("ToolbarStackOverflowTest", "测试已重置");
        }
    }
}
