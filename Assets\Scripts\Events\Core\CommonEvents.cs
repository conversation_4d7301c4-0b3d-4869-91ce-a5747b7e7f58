using UnityEngine;
using System;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 菜单项点击事件
    /// </summary>
    public class MenuItemClickedEvent : EventBase
    {
        public string MenuItem { get; set; }
        public object Data { get; set; }

        public MenuItemClickedEvent(string menuItem, object data = null, string source = "MenuBar")
            : base(source)
        {
            MenuItem = menuItem;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            MenuItem = null;
            Data = null;
        }
    }

    /// <summary>
    /// 工具选择事件
    /// </summary>
    public class ToolSelectedEvent : EventBase
    {
        public string ToolName { get; set; }
        public object ToolData { get; set; }

        public ToolSelectedEvent(string toolName, object toolData = null, string source = "Toolbar")
            : base(source)
        {
            ToolName = toolName;
            ToolData = toolData;
        }

        public override void Reset()
        {
            base.Reset();
            ToolName = null;
            ToolData = null;
        }
    }

    /// <summary>
    /// 对象选择事件
    /// </summary>
    public class ObjectSelectedEvent : EventBase
    {
        public object SelectedObject { get; set; }
        public bool IsMultiSelect { get; set; }
        public bool IsAddToSelection { get; set; }

        public ObjectSelectedEvent(object selectedObject, bool isMultiSelect = false, bool isAddToSelection = false, string source = "SelectionManager")
            : base(source)
        {
            SelectedObject = selectedObject;
            IsMultiSelect = isMultiSelect;
            IsAddToSelection = isAddToSelection;
        }

        public override void Reset()
        {
            base.Reset();
            SelectedObject = null;
            IsMultiSelect = false;
            IsAddToSelection = false;
        }
    }

    /// <summary>
    /// 状态消息事件
    /// </summary>
    public class StatusMessageEvent : EventBase
    {
        public string Message { get; set; }
        public StatusMessageType MessageType { get; set; }
        public float Duration { get; set; }

        public StatusMessageEvent(string message, StatusMessageType messageType = StatusMessageType.Info, float duration = 3f, string source = "StatusBar")
            : base(source)
        {
            Message = message;
            MessageType = messageType;
            Duration = duration;
        }

        public override void Reset()
        {
            base.Reset();
            Message = null;
            MessageType = StatusMessageType.Info;
            Duration = 3f;
        }
    }

    /// <summary>
    /// 面板事件
    /// </summary>
    public class PanelEvent : EventBase
    {
        public string PanelName { get; set; }
        public PanelAction Action { get; set; }
        public object Data { get; set; }

        public PanelEvent(string panelName, PanelAction action, object data = null, string source = "PanelManager")
            : base(source)
        {
            PanelName = panelName;
            Action = action;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            PanelName = null;
            Action = PanelAction.Show;
            Data = null;
        }
    }

    /// <summary>
    /// 属性变更事件
    /// </summary>
    public class PropertyChangedEvent : EventBase
    {
        public object Target { get; set; }
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }

        public PropertyChangedEvent(object target, string propertyName, object oldValue, object newValue, string source = "PropertyPanel")
            : base(source)
        {
            Target = target;
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }

        public override void Reset()
        {
            base.Reset();
            Target = null;
            PropertyName = null;
            OldValue = null;
            NewValue = null;
        }
    }

    /// <summary>
    /// 自定义事件
    /// </summary>
    public class CustomEvent : EventBase
    {
        public string EventName { get; set; }
        public object Data { get; set; }

        public CustomEvent(string eventName, object data = null, string source = "Custom")
            : base(source)
        {
            EventName = eventName;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            EventName = null;
            Data = null;
        }
    }

    #region 工具栏相关事件

    /// <summary>
    /// 工具栏可见性变更事件
    /// </summary>
    public class ToolbarVisibilityChangedEvent : EventBase
    {
        public bool IsVisible { get; set; }

        public ToolbarVisibilityChangedEvent(bool isVisible, string source = "Toolbar")
            : base(source)
        {
            IsVisible = isVisible;
        }

        public override void Reset()
        {
            base.Reset();
            IsVisible = true;
        }
    }

    #endregion

    #region 面板相关事件

    /// <summary>
    /// 面板折叠事件
    /// </summary>
    public class PanelCollapsedEvent : EventBase
    {
        public string PanelName { get; set; }
        public bool IsCollapsed { get; set; }

        public PanelCollapsedEvent(string panelName, bool isCollapsed, string source = "PanelManager")
            : base(source)
        {
            PanelName = panelName;
            IsCollapsed = isCollapsed;
        }

        public override void Reset()
        {
            base.Reset();
            PanelName = null;
            IsCollapsed = false;
        }
    }

    /// <summary>
    /// 面板调整大小事件
    /// </summary>
    public class PanelResizedEvent : EventBase
    {
        public string PanelName { get; set; }
        public float NewSize { get; set; }

        public PanelResizedEvent(string panelName, float newSize, string source = "PanelManager")
            : base(source)
        {
            PanelName = panelName;
            NewSize = newSize;
        }

        public override void Reset()
        {
            base.Reset();
            PanelName = null;
            NewSize = 0f;
        }
    }

    /// <summary>
    /// 面板可见性变更事件
    /// </summary>
    public class PanelVisibilityChangedEvent : EventBase
    {
        public string PanelName { get; set; }
        public bool IsVisible { get; set; }

        public PanelVisibilityChangedEvent(string panelName, bool isVisible, string source = "PanelManager")
            : base(source)
        {
            PanelName = panelName;
            IsVisible = isVisible;
        }

        public override void Reset()
        {
            base.Reset();
            PanelName = null;
            IsVisible = true;
        }
    }

    #endregion

    #region 选择相关事件

    /// <summary>
    /// 多对象选择事件
    /// </summary>
    public class MultipleObjectsSelectedEvent : EventBase
    {
        public object[] SelectedObjects { get; set; }

        public MultipleObjectsSelectedEvent(object[] selectedObjects, string source = "SelectionManager")
            : base(source)
        {
            SelectedObjects = selectedObjects;
        }

        public override void Reset()
        {
            base.Reset();
            SelectedObjects = null;
        }
    }

    /// <summary>
    /// 选择清除事件
    /// </summary>
    public class SelectionClearedEvent : EventBase
    {
        public SelectionClearedEvent(string source = "SelectionManager")
            : base(source)
        {
        }
    }

    #endregion

    #region 状态栏相关事件

    /// <summary>
    /// 鼠标世界坐标变更事件
    /// </summary>
    public class MouseWorldPositionChangedEvent : EventBase
    {
        public Vector3 WorldPosition { get; set; }

        public MouseWorldPositionChangedEvent(Vector3 worldPosition, string source = "StatusBar")
            : base(source)
        {
            WorldPosition = worldPosition;
        }

        public override void Reset()
        {
            base.Reset();
            WorldPosition = Vector3.zero;
        }
    }

    /// <summary>
    /// 鼠标地理坐标变更事件
    /// </summary>
    public class MouseGeoPositionChangedEvent : EventBase
    {
        public Vector2 GeoPosition { get; set; }

        public MouseGeoPositionChangedEvent(Vector2 geoPosition, string source = "StatusBar")
            : base(source)
        {
            GeoPosition = geoPosition;
        }

        public override void Reset()
        {
            base.Reset();
            GeoPosition = Vector2.zero;
        }
    }

    /// <summary>
    /// 工具提示变更事件
    /// </summary>
    public class ToolTipChangedEvent : EventBase
    {
        public string ToolTip { get; set; }

        public ToolTipChangedEvent(string toolTip, string source = "StatusBar")
            : base(source)
        {
            ToolTip = toolTip;
        }

        public override void Reset()
        {
            base.Reset();
            ToolTip = null;
        }
    }

    #endregion

    #region 属性相关事件

    /// <summary>
    /// 属性编辑器请求事件
    /// </summary>
    public class PropertyEditorRequestedEvent : EventBase
    {
        public Type PropertyType { get; set; }

        public PropertyEditorRequestedEvent(Type propertyType, string source = "PropertyPanel")
            : base(source)
        {
            PropertyType = propertyType;
        }

        public override void Reset()
        {
            base.Reset();
            PropertyType = null;
        }
    }

    /// <summary>
    /// 属性刷新请求事件
    /// </summary>
    public class PropertiesRefreshRequestedEvent : EventBase
    {
        public PropertiesRefreshRequestedEvent(string source = "PropertyPanel")
            : base(source)
        {
        }
    }

    #endregion

    #region UI交互事件

    /// <summary>
    /// 按钮点击事件
    /// </summary>
    public class ButtonClickEvent : EventBase
    {
        public string ButtonName { get; set; }
        public string ButtonId { get; set; }
        public object ButtonData { get; set; }
        public Vector2 ClickPosition { get; set; }

        public ButtonClickEvent(string buttonName, string buttonId = null, object buttonData = null, Vector2 clickPosition = default, string source = "UI")
            : base(source)
        {
            ButtonName = buttonName;
            ButtonId = buttonId;
            ButtonData = buttonData;
            ClickPosition = clickPosition;
        }

        public override void Reset()
        {
            base.Reset();
            ButtonName = null;
            ButtonId = null;
            ButtonData = null;
            ClickPosition = Vector2.zero;
        }
    }

    /// <summary>
    /// 菜单选择事件
    /// </summary>
    public class MenuSelectEvent : EventBase
    {
        public string MenuId { get; set; }
        public string MenuPath { get; set; }
        public object MenuData { get; set; }
        public bool IsSubMenu { get; set; }

        public MenuSelectEvent(string menuId, string menuPath = null, object menuData = null, bool isSubMenu = false, string source = "MenuBar")
            : base(source)
        {
            MenuId = menuId;
            MenuPath = menuPath;
            MenuData = menuData;
            IsSubMenu = isSubMenu;
        }

        public override void Reset()
        {
            base.Reset();
            MenuId = null;
            MenuPath = null;
            MenuData = null;
            IsSubMenu = false;
        }
    }

    /// <summary>
    /// UI元素悬停事件
    /// </summary>
    public class UIElementHoverEvent : EventBase
    {
        public string ElementName { get; set; }
        public string ElementId { get; set; }
        public bool IsEntering { get; set; }
        public Vector2 HoverPosition { get; set; }

        public UIElementHoverEvent(string elementName, string elementId = null, bool isEntering = true, Vector2 hoverPosition = default, string source = "UI")
            : base(source)
        {
            ElementName = elementName;
            ElementId = elementId;
            IsEntering = isEntering;
            HoverPosition = hoverPosition;
        }

        public override void Reset()
        {
            base.Reset();
            ElementName = null;
            ElementId = null;
            IsEntering = true;
            HoverPosition = Vector2.zero;
        }
    }

    /// <summary>
    /// UI元素焦点变更事件
    /// </summary>
    public class UIElementFocusEvent : EventBase
    {
        public string ElementName { get; set; }
        public string ElementId { get; set; }
        public bool HasFocus { get; set; }

        public UIElementFocusEvent(string elementName, string elementId = null, bool hasFocus = true, string source = "UI")
            : base(source)
        {
            ElementName = elementName;
            ElementId = elementId;
            HasFocus = hasFocus;
        }

        public override void Reset()
        {
            base.Reset();
            ElementName = null;
            ElementId = null;
            HasFocus = true;
        }
    }

    /// <summary>
    /// 下拉菜单状态变更事件
    /// </summary>
    public class DropdownStateChangedEvent : EventBase
    {
        public string DropdownName { get; set; }
        public bool IsOpen { get; set; }
        public string SelectedValue { get; set; }

        public DropdownStateChangedEvent(string dropdownName, bool isOpen, string selectedValue = null, string source = "UI")
            : base(source)
        {
            DropdownName = dropdownName;
            IsOpen = isOpen;
            SelectedValue = selectedValue;
        }

        public override void Reset()
        {
            base.Reset();
            DropdownName = null;
            IsOpen = false;
            SelectedValue = null;
        }
    }

    /// <summary>
    /// 标签页切换事件
    /// </summary>
    public class TabSwitchEvent : EventBase
    {
        public string TabGroupName { get; set; }
        public string PreviousTabId { get; set; }
        public string CurrentTabId { get; set; }
        public object TabData { get; set; }

        public TabSwitchEvent(string tabGroupName, string currentTabId, string previousTabId = null, object tabData = null, string source = "UI")
            : base(source)
        {
            TabGroupName = tabGroupName;
            CurrentTabId = currentTabId;
            PreviousTabId = previousTabId;
            TabData = tabData;
        }

        public override void Reset()
        {
            base.Reset();
            TabGroupName = null;
            PreviousTabId = null;
            CurrentTabId = null;
            TabData = null;
        }
    }

    #endregion

    /// <summary>
    /// 输入事件
    /// </summary>
    public class InputEvent : EventBase
    {
        public InputEventType InputType { get; set; }
        public Vector2 Position { get; set; }
        public KeyCode KeyCode { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }

        public InputEvent(InputEventType inputType, Vector2 position = default, KeyCode keyCode = KeyCode.None, string source = "InputManager")
            : base(source)
        {
            InputType = inputType;
            Position = position;
            KeyCode = keyCode;
        }

        public override void Reset()
        {
            base.Reset();
            InputType = InputEventType.Mouse;
            Position = Vector2.zero;
            KeyCode = KeyCode.None;
            IsPressed = false;
            IsReleased = false;
        }
    }

    #region 枚举定义

    public enum StatusMessageType
    {
        Info,
        Warning,
        Error,
        Success
    }

    public enum PanelAction
    {
        Show,
        Hide,
        Toggle,
        Focus,
        Refresh
    }

    public enum InputEventType
    {
        Mouse,
        Keyboard,
        Touch,
        Gamepad
    }

    public enum MouseButton
    {
        Left,
        Right,
        Middle,
        Forward,
        Back
    }

    #endregion

    /// <summary>
    /// 鼠标输入事件
    /// </summary>
    public class MouseInputEvent : EventBase
    {
        public Vector2 Position { get; set; }
        public MouseButton Button { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }
        public float ScrollDelta { get; set; }

        public MouseInputEvent(Vector2 position, MouseButton button, bool isPressed = false, bool isReleased = false, string source = "InputManager")
            : base(source)
        {
            Position = position;
            Button = button;
            IsPressed = isPressed;
            IsReleased = isReleased;
        }

        public override void Reset()
        {
            base.Reset();
            Position = Vector2.zero;
            Button = MouseButton.Left;
            IsPressed = false;
            IsReleased = false;
            ScrollDelta = 0f;
        }
    }

    /// <summary>
    /// 键盘输入事件
    /// </summary>
    public class KeyboardInputEvent : EventBase
    {
        public KeyCode KeyCode { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }
        public bool IsHeld { get; set; }
        public bool CtrlPressed { get; set; }
        public bool ShiftPressed { get; set; }
        public bool AltPressed { get; set; }

        public KeyboardInputEvent(KeyCode keyCode, bool isPressed = false, bool isReleased = false, string source = "InputManager")
            : base(source)
        {
            KeyCode = keyCode;
            IsPressed = isPressed;
            IsReleased = isReleased;
        }

        public override void Reset()
        {
            base.Reset();
            KeyCode = KeyCode.None;
            IsPressed = false;
            IsReleased = false;
            IsHeld = false;
            CtrlPressed = false;
            ShiftPressed = false;
            AltPressed = false;
        }
    }

    #region 具体输入事件类型

    /// <summary>
    /// 按键按下事件
    /// </summary>
    public class KeyPressEvent : EventBase
    {
        public KeyCode KeyCode { get; set; }
        public bool CtrlPressed { get; set; }
        public bool ShiftPressed { get; set; }
        public bool AltPressed { get; set; }
        public float HoldDuration { get; set; }

        public KeyPressEvent(KeyCode keyCode, bool ctrlPressed = false, bool shiftPressed = false, bool altPressed = false, string source = "InputManager")
            : base(source)
        {
            KeyCode = keyCode;
            CtrlPressed = ctrlPressed;
            ShiftPressed = shiftPressed;
            AltPressed = altPressed;
        }

        public override void Reset()
        {
            base.Reset();
            KeyCode = KeyCode.None;
            CtrlPressed = false;
            ShiftPressed = false;
            AltPressed = false;
            HoldDuration = 0f;
        }
    }

    /// <summary>
    /// 按键释放事件
    /// </summary>
    public class KeyReleaseEvent : EventBase
    {
        public KeyCode KeyCode { get; set; }
        public float HoldDuration { get; set; }

        public KeyReleaseEvent(KeyCode keyCode, float holdDuration = 0f, string source = "InputManager")
            : base(source)
        {
            KeyCode = keyCode;
            HoldDuration = holdDuration;
        }

        public override void Reset()
        {
            base.Reset();
            KeyCode = KeyCode.None;
            HoldDuration = 0f;
        }
    }

    /// <summary>
    /// 鼠标点击事件
    /// </summary>
    public class MouseClickEvent : EventBase
    {
        public Vector2 Position { get; set; }
        public MouseButton Button { get; set; }
        public int ClickCount { get; set; }
        public bool CtrlPressed { get; set; }
        public bool ShiftPressed { get; set; }
        public bool AltPressed { get; set; }

        public MouseClickEvent(Vector2 position, MouseButton button = MouseButton.Left, int clickCount = 1, string source = "InputManager")
            : base(source)
        {
            Position = position;
            Button = button;
            ClickCount = clickCount;
        }

        public override void Reset()
        {
            base.Reset();
            Position = Vector2.zero;
            Button = MouseButton.Left;
            ClickCount = 1;
            CtrlPressed = false;
            ShiftPressed = false;
            AltPressed = false;
        }
    }

    /// <summary>
    /// 鼠标拖拽事件
    /// </summary>
    public class MouseDragEvent : EventBase
    {
        public Vector2 StartPosition { get; set; }
        public Vector2 CurrentPosition { get; set; }
        public Vector2 DeltaPosition { get; set; }
        public MouseButton Button { get; set; }
        public bool IsDragStart { get; set; }
        public bool IsDragEnd { get; set; }

        public MouseDragEvent(Vector2 startPosition, Vector2 currentPosition, MouseButton button = MouseButton.Left, bool isDragStart = false, bool isDragEnd = false, string source = "InputManager")
            : base(source)
        {
            StartPosition = startPosition;
            CurrentPosition = currentPosition;
            DeltaPosition = currentPosition - startPosition;
            Button = button;
            IsDragStart = isDragStart;
            IsDragEnd = isDragEnd;
        }

        public override void Reset()
        {
            base.Reset();
            StartPosition = Vector2.zero;
            CurrentPosition = Vector2.zero;
            DeltaPosition = Vector2.zero;
            Button = MouseButton.Left;
            IsDragStart = false;
            IsDragEnd = false;
        }
    }

    /// <summary>
    /// 鼠标滚轮事件
    /// </summary>
    public class MouseScrollEvent : EventBase
    {
        public Vector2 Position { get; set; }
        public float ScrollDelta { get; set; }
        public Vector2 ScrollDirection { get; set; }

        public MouseScrollEvent(Vector2 position, float scrollDelta, string source = "InputManager")
            : base(source)
        {
            Position = position;
            ScrollDelta = scrollDelta;
            ScrollDirection = scrollDelta > 0 ? Vector2.up : Vector2.down;
        }

        public override void Reset()
        {
            base.Reset();
            Position = Vector2.zero;
            ScrollDelta = 0f;
            ScrollDirection = Vector2.zero;
        }
    }

    /// <summary>
    /// 触摸输入事件
    /// </summary>
    public class TouchInputEvent : EventBase
    {
        public int TouchId { get; set; }
        public Vector2 Position { get; set; }
        public TouchPhase Phase { get; set; }
        public float Pressure { get; set; }
        public Vector2 DeltaPosition { get; set; }

        public TouchInputEvent(int touchId, Vector2 position, TouchPhase phase, string source = "InputManager")
            : base(source)
        {
            TouchId = touchId;
            Position = position;
            Phase = phase;
        }

        public override void Reset()
        {
            base.Reset();
            TouchId = 0;
            Position = Vector2.zero;
            Phase = TouchPhase.Began;
            Pressure = 0f;
            DeltaPosition = Vector2.zero;
        }
    }

    /// <summary>
    /// 手柄输入事件
    /// </summary>
    public class GamepadInputEvent : EventBase
    {
        public int GamepadId { get; set; }
        public string ButtonName { get; set; }
        public float AxisValue { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }

        public GamepadInputEvent(int gamepadId, string buttonName, float axisValue = 0f, bool isPressed = false, bool isReleased = false, string source = "InputManager")
            : base(source)
        {
            GamepadId = gamepadId;
            ButtonName = buttonName;
            AxisValue = axisValue;
            IsPressed = isPressed;
            IsReleased = isReleased;
        }

        public override void Reset()
        {
            base.Reset();
            GamepadId = 0;
            ButtonName = null;
            AxisValue = 0f;
            IsPressed = false;
            IsReleased = false;
        }
    }

    #endregion
}
