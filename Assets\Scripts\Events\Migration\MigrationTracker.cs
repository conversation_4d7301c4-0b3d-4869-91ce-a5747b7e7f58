using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 迁移进度跟踪器
    /// 用于跟踪和报告事件系统迁移的进度
    /// </summary>
    [Serializable]
    public class MigrationTracker
    {
        [SerializeField] private List<MigrationItem> _migrationItems = new();
        [SerializeField] private MigrationPhase _currentPhase = MigrationPhase.NotStarted;
        [SerializeField] private DateTime _migrationStartTime;

        public MigrationPhase CurrentPhase => _currentPhase;
        public DateTime MigrationStartTime => _migrationStartTime;

        public void StartMigration()
        {
            _migrationStartTime = DateTime.Now;
            _currentPhase = MigrationPhase.Preparation;

            InitializeMigrationItems();

            Debug.Log($"[MigrationTracker] 开始事件系统迁移 - {_migrationStartTime}");
        }

        public void UpdatePhase(MigrationPhase newPhase)
        {
            _currentPhase = newPhase;
            Debug.Log($"[MigrationTracker] 迁移阶段更新: {newPhase}");
        }

        public void MarkItemCompleted(string itemName)
        {
            var item = _migrationItems.FirstOrDefault(i => i.Name == itemName);
            if (item != null)
            {
                item.Status = MigrationStatus.Completed;
                item.CompletionTime = DateTime.Now;
                Debug.Log($"[MigrationTracker] 迁移项目完成: {itemName}");
            }
        }

        public void MarkItemInProgress(string itemName)
        {
            var item = _migrationItems.FirstOrDefault(i => i.Name == itemName);
            if (item != null)
            {
                item.Status = MigrationStatus.InProgress;
                item.StartTime = DateTime.Now;
                Debug.Log($"[MigrationTracker] 迁移项目开始: {itemName}");
            }
        }

        public void MarkItemFailed(string itemName, string errorMessage)
        {
            var item = _migrationItems.FirstOrDefault(i => i.Name == itemName);
            if (item != null)
            {
                item.Status = MigrationStatus.Failed;
                item.ErrorMessage = errorMessage;
                Debug.LogError($"[MigrationTracker] 迁移项目失败: {itemName} - {errorMessage}");
            }
        }

        public MigrationReport GenerateReport()
        {
            var report = new MigrationReport
            {
                CurrentPhase = _currentPhase,
                StartTime = _migrationStartTime,
                Items = _migrationItems.ToList()
            };

            var completedItems = _migrationItems.Where(i => i.Status == MigrationStatus.Completed).ToList();
            var inProgressItems = _migrationItems.Where(i => i.Status == MigrationStatus.InProgress).ToList();
            var failedItems = _migrationItems.Where(i => i.Status == MigrationStatus.Failed).ToList();

            report.CompletedCount = completedItems.Count;
            report.InProgressCount = inProgressItems.Count;
            report.FailedCount = failedItems.Count;
            report.TotalCount = _migrationItems.Count;
            report.ProgressPercentage = (float)completedItems.Count / _migrationItems.Count * 100f;

            return report;
        }

        public string GetProgressSummary()
        {
            var report = GenerateReport();
            return $"迁移进度: {report.CompletedCount}/{report.TotalCount} ({report.ProgressPercentage:F1}%) - " +
                   $"当前阶段: {report.CurrentPhase} - " +
                   $"进行中: {report.InProgressCount}, 失败: {report.FailedCount}";
        }

        private void InitializeMigrationItems()
        {
            _migrationItems.Clear();

            // 阶段一：准备和设计
            _migrationItems.Add(new MigrationItem("兼容性层设计", MigrationPhase.Preparation, MigrationPriority.High));
            _migrationItems.Add(new MigrationItem("事件类型统一设计", MigrationPhase.Preparation, MigrationPriority.High));

            // 阶段二：核心UI组件迁移
            _migrationItems.Add(new MigrationItem("Toolbar组件迁移", MigrationPhase.CoreUIMigration, MigrationPriority.High));
            _migrationItems.Add(new MigrationItem("StatusBar组件迁移", MigrationPhase.CoreUIMigration, MigrationPriority.High));
            _migrationItems.Add(new MigrationItem("MenuBar组件迁移", MigrationPhase.CoreUIMigration, MigrationPriority.High));
            _migrationItems.Add(new MigrationItem("LeftPanel组件迁移", MigrationPhase.CoreUIMigration, MigrationPriority.High));

            // 阶段三：输入系统整合
            _migrationItems.Add(new MigrationItem("InputEventHandler迁移", MigrationPhase.InputSystemIntegration, MigrationPriority.Medium));
            _migrationItems.Add(new MigrationItem("输入优先级管理优化", MigrationPhase.InputSystemIntegration, MigrationPriority.Medium));

            // 阶段四：DaisyUI组件迁移
            _migrationItems.Add(new MigrationItem("DaisyTree组件迁移", MigrationPhase.DaisyUIMigration, MigrationPriority.Medium));
            _migrationItems.Add(new MigrationItem("DaisyInput组件迁移", MigrationPhase.DaisyUIMigration, MigrationPriority.Medium));
            _migrationItems.Add(new MigrationItem("其他DaisyUI组件迁移", MigrationPhase.DaisyUIMigration, MigrationPriority.Low));

            // 阶段五：系统清理和优化
            _migrationItems.Add(new MigrationItem("旧系统清理", MigrationPhase.CleanupAndOptimization, MigrationPriority.Low));
            _migrationItems.Add(new MigrationItem("性能优化", MigrationPhase.CleanupAndOptimization, MigrationPriority.Medium));
            _migrationItems.Add(new MigrationItem("文档更新", MigrationPhase.CleanupAndOptimization, MigrationPriority.Low));

            Debug.Log($"[MigrationTracker] 初始化了 {_migrationItems.Count} 个迁移项目");
        }

        public List<MigrationItem> GetItemsByPhase(MigrationPhase phase)
        {
            return _migrationItems.Where(i => i.Phase == phase).ToList();
        }

        public List<MigrationItem> GetItemsByStatus(MigrationStatus status)
        {
            return _migrationItems.Where(i => i.Status == status).ToList();
        }

        public List<MigrationItem> GetItemsByPriority(MigrationPriority priority)
        {
            return _migrationItems.Where(i => i.Priority == priority).ToList();
        }
    }

    [Serializable]
    public class MigrationItem
    {
        public string Name;
        public MigrationPhase Phase;
        public MigrationPriority Priority;
        public MigrationStatus Status;
        public DateTime StartTime;
        public DateTime CompletionTime;
        public string ErrorMessage;

        public MigrationItem(string name, MigrationPhase phase, MigrationPriority priority)
        {
            Name = name;
            Phase = phase;
            Priority = priority;
            Status = MigrationStatus.NotStarted;
        }

        public TimeSpan GetDuration()
        {
            if (Status == MigrationStatus.Completed)
            {
                return CompletionTime - StartTime;
            }
            else if (Status == MigrationStatus.InProgress)
            {
                return DateTime.Now - StartTime;
            }
            return TimeSpan.Zero;
        }
    }

    public class MigrationReport
    {
        public MigrationPhase CurrentPhase;
        public DateTime StartTime;
        public List<MigrationItem> Items;
        public int CompletedCount;
        public int InProgressCount;
        public int FailedCount;
        public int TotalCount;
        public float ProgressPercentage;

        public string GenerateDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 事件系统迁移报告 ===");
            report.AppendLine($"开始时间: {StartTime}");
            report.AppendLine($"当前阶段: {CurrentPhase}");
            report.AppendLine($"总进度: {CompletedCount}/{TotalCount} ({ProgressPercentage:F1}%)");
            report.AppendLine($"状态分布: 完成({CompletedCount}) 进行中({InProgressCount}) 失败({FailedCount})");
            report.AppendLine();

            foreach (var phase in Enum.GetValues(typeof(MigrationPhase)).Cast<MigrationPhase>())
            {
                if (phase == MigrationPhase.NotStarted) continue;

                var phaseItems = Items.Where(i => i.Phase == phase).ToList();
                if (phaseItems.Any())
                {
                    report.AppendLine($"--- {phase} ---");
                    foreach (var item in phaseItems)
                    {
                        var statusIcon = GetStatusIcon(item.Status);
                        var duration = item.GetDuration();
                        report.AppendLine($"{statusIcon} {item.Name} ({item.Priority}) - {duration.TotalHours:F1}h");

                        if (!string.IsNullOrEmpty(item.ErrorMessage))
                        {
                            report.AppendLine($"    错误: {item.ErrorMessage}");
                        }
                    }
                    report.AppendLine();
                }
            }

            return report.ToString();
        }

        private string GetStatusIcon(MigrationStatus status) => status switch
        {
            MigrationStatus.Completed => "✅",
            MigrationStatus.InProgress => "🔄",
            MigrationStatus.Failed => "❌",
            _ => "⏳"
        };
    }

    public enum MigrationPhase
    {
        NotStarted,
        Preparation,
        CoreUIMigration,
        InputSystemIntegration,
        DaisyUIMigration,
        CleanupAndOptimization,
        Completed
    }

    public enum MigrationStatus
    {
        NotStarted,
        InProgress,
        Completed,
        Failed
    }


}