using System;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件订阅的抽象基类
    /// </summary>
    public abstract class AbstractEventSubscription : IEventSubscription
    {
        public bool IsValid { get; private set; } = true;
        public Type EventType { get; protected set; }
        public int Priority { get; protected set; }
        public DateTime SubscribeTime { get; protected set; }

        protected AbstractEventSubscription(Type eventType, int priority)
        {
            EventType = eventType;
            Priority = priority;
            SubscribeTime = DateTime.UtcNow;
        }

        public abstract void Invoke(IEvent eventData);

        public virtual void Dispose()
        {
            IsValid = false;
        }
    }

    /// <summary>
    /// 泛型事件订阅实现
    /// </summary>
    internal class EventSubscription<T> : AbstractEventSubscription where T : IEvent
    {
        public Action<T> Handler { get; private set; }
        public Func<T, bool> Filter { get; private set; }

        public EventSubscription(Action<T> handler, Func<T, bool> filter, int priority, Type eventType)
            : base(eventType, priority)
        {
            Handler = handler ?? throw new ArgumentNullException(nameof(handler));
            Filter = filter;
        }

        public override void Invoke(IEvent eventData)
        {
            if (!IsValid || eventData == null) return;

            if (eventData is T typedEvent)
            {
                // 应用过滤器
                if (Filter != null && !Filter(typedEvent)) return;

                Handler(typedEvent);
            }
        }

        public override void Dispose()
        {
            base.Dispose();
            Handler = null;
            Filter = null;
        }
    }
}