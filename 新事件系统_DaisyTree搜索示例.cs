// 新事件系统下的DaisyTree搜索功能实现示例
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.EventSystem.Examples
{
    #region 事件定义
    
    /// <summary>
    /// 基础事件接口
    /// </summary>
    public interface IEvent
    {
        DateTime Timestamp { get; }
        string EventId { get; }
    }
    
    /// <summary>
    /// 树搜索事件
    /// </summary>
    public class TreeSearchEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public string SearchQuery { get; }
        public int MinQueryLength { get; }
        public bool IsRealTimeSearch { get; }
        
        public TreeSearchEvent(string componentId, string searchQuery, int minQueryLength = 2, bool isRealTimeSearch = true)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            SearchQuery = searchQuery ?? string.Empty;
            MinQueryLength = minQueryLength;
            IsRealTimeSearch = isRealTimeSearch;
        }
    }
    
    /// <summary>
    /// 树数据过滤事件
    /// </summary>
    public class TreeDataFilterEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public IEnumerable<DaisyTreeData> FilteredData { get; }
        public string SearchQuery { get; }
        public int TotalItems { get; }
        public int FilteredItems { get; }
        
        public TreeDataFilterEvent(string componentId, IEnumerable<DaisyTreeData> filteredData, string searchQuery)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            FilteredData = filteredData;
            SearchQuery = searchQuery;
            TotalItems = CountAllItems(filteredData);
            FilteredItems = CountMatchingItems(filteredData, searchQuery);
        }
        
        private int CountAllItems(IEnumerable<DaisyTreeData> items)
        {
            int count = 0;
            foreach (var item in items)
            {
                count++;
                if (item.Children != null)
                    count += CountAllItems(item.Children);
            }
            return count;
        }
        
        private int CountMatchingItems(IEnumerable<DaisyTreeData> items, string query)
        {
            if (string.IsNullOrWhiteSpace(query)) return CountAllItems(items);
            
            int count = 0;
            foreach (var item in items)
            {
                if (item.Text?.ToLower().Contains(query.ToLower()) == true ||
                    item.Icon?.ToLower().Contains(query.ToLower()) == true)
                    count++;
                    
                if (item.Children != null)
                    count += CountMatchingItems(item.Children, query);
            }
            return count;
        }
    }
    
    /// <summary>
    /// 树显示更新事件
    /// </summary>
    public class TreeDisplayUpdateEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public bool IsEmpty { get; }
        public int VisibleItemCount { get; }
        public string SearchQuery { get; }
        
        public TreeDisplayUpdateEvent(string componentId, bool isEmpty, int visibleItemCount, string searchQuery)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            IsEmpty = isEmpty;
            VisibleItemCount = visibleItemCount;
            SearchQuery = searchQuery;
        }
    }
    
    /// <summary>
    /// 搜索性能监控事件
    /// </summary>
    public class SearchPerformanceEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public TimeSpan SearchDuration { get; }
        public int TotalItemsSearched { get; }
        public int ResultCount { get; }
        public string SearchQuery { get; }
        
        public SearchPerformanceEvent(string componentId, TimeSpan searchDuration, int totalItemsSearched, int resultCount, string searchQuery)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            SearchDuration = searchDuration;
            TotalItemsSearched = totalItemsSearched;
            ResultCount = resultCount;
            SearchQuery = searchQuery;
        }
    }
    
    #endregion
    
    #region 新事件系统接口
    
    /// <summary>
    /// 高性能事件总线接口
    /// </summary>
    public interface IEventBus
    {
        // 基础订阅/取消订阅
        void Subscribe<T>(Action<T> handler) where T : IEvent;
        void Unsubscribe<T>(Action<T> handler) where T : IEvent;
        
        // 条件订阅
        void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent;
        
        // 优先级订阅
        void Subscribe<T>(Action<T> handler, int priority) where T : IEvent;
        
        // 事件发布
        void Publish<T>(T eventData) where T : IEvent;
        void PublishAsync<T>(T eventData) where T : IEvent;
        
        // 延迟发布
        void PublishDelayed<T>(T eventData, float delay) where T : IEvent;
        
        // 批量发布
        void PublishBatch<T>(IEnumerable<T> events) where T : IEvent;
        
        // 生命周期管理
        void ClearAllSubscriptions();
        void ClearSubscriptions<T>() where T : IEvent;
    }
    
    /// <summary>
    /// 事件订阅管理器接口
    /// </summary>
    public interface IEventSubscriptionManager : IDisposable
    {
        void Subscribe<T>(Action<T> handler) where T : IEvent;
        void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent;
        void Subscribe<T>(Action<T> handler, int priority) where T : IEvent;
        void UnsubscribeAll();
    }
    
    #endregion
    
    #region 搜索事件处理器
    
    /// <summary>
    /// 树搜索事件处理器
    /// </summary>
    public class TreeSearchEventHandler
    {
        private readonly IEventBus _eventBus;
        private readonly IEventSubscriptionManager _subscriptionManager;
        private readonly Dictionary<string, List<DaisyTreeData>> _componentDataCache;
        private readonly Dictionary<string, TreeSearchConfig> _componentConfigs;
        
        public TreeSearchEventHandler(IEventBus eventBus, IEventSubscriptionManager subscriptionManager)
        {
            _eventBus = eventBus;
            _subscriptionManager = subscriptionManager;
            _componentDataCache = new Dictionary<string, List<DaisyTreeData>>();
            _componentConfigs = new Dictionary<string, TreeSearchConfig>();
            
            // 订阅搜索事件
            _subscriptionManager.Subscribe<TreeSearchEvent>(HandleSearchEvent);
        }
        
        public void RegisterComponent(string componentId, List<DaisyTreeData> treeData, TreeSearchConfig config = null)
        {
            _componentDataCache[componentId] = treeData;
            _componentConfigs[componentId] = config ?? new TreeSearchConfig();
        }
        
        public void UnregisterComponent(string componentId)
        {
            _componentDataCache.Remove(componentId);
            _componentConfigs.Remove(componentId);
        }
        
        private void HandleSearchEvent(TreeSearchEvent searchEvent)
        {
            if (!_componentDataCache.ContainsKey(searchEvent.ComponentId))
            {
                Debug.LogWarning($"Component {searchEvent.ComponentId} not registered with TreeSearchEventHandler");
                return;
            }
            
            var startTime = DateTime.Now;
            var treeData = _componentDataCache[searchEvent.ComponentId];
            var config = _componentConfigs[searchEvent.ComponentId];
            
            // 应用防抖机制
            if (searchEvent.IsRealTimeSearch && 
                searchEvent.SearchQuery.Length > 0 && 
                searchEvent.SearchQuery.Length < searchEvent.MinQueryLength)
            {
                return;
            }
            
            // 执行搜索过滤
            var filteredData = FilterTreeData(treeData, searchEvent.SearchQuery, config);
            var filteredList = filteredData.ToList();
            
            // 发布过滤结果事件
            _eventBus.Publish(new TreeDataFilterEvent(
                searchEvent.ComponentId, 
                filteredList, 
                searchEvent.SearchQuery
            ));
            
            // 发布性能监控事件
            var searchDuration = DateTime.Now - startTime;
            _eventBus.Publish(new SearchPerformanceEvent(
                searchEvent.ComponentId,
                searchDuration,
                CountAllItems(treeData),
                CountAllItems(filteredList),
                searchEvent.SearchQuery
            ));
        }
        
        private IEnumerable<DaisyTreeData> FilterTreeData(List<DaisyTreeData> items, string query, TreeSearchConfig config)
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                foreach (var item in items)
                    yield return item;
                yield break;
            }
            
            var lowerQuery = query.ToLower();
            
            foreach (var item in items)
            {
                bool matches = false;
                
                // 文本匹配
                if (config.SearchInText && item.Text?.ToLower().Contains(lowerQuery) == true)
                    matches = true;
                
                // 图标匹配
                if (config.SearchInIcon && item.Icon?.ToLower().Contains(lowerQuery) == true)
                    matches = true;
                
                // 检查子项
                List<DaisyTreeData> filteredChildren = null;
                if (item.Children != null && item.Children.Count > 0)
                {
                    var childResults = FilterTreeData(item.Children, query, config);
                    var childList = childResults.ToList();
                    if (childList.Count > 0)
                    {
                        filteredChildren = childList;
                    }
                }
                
                // 如果当前项匹配或有匹配的子项，则包含此项
                if (matches || filteredChildren != null)
                {
                    var filteredItem = new DaisyTreeData(item.Id, item.Text, item.Icon)
                    {
                        Parent = item.Parent,
                        IsExpanded = item.IsExpanded || (filteredChildren != null && config.AutoExpandMatches),
                        IsSelected = item.IsSelected,
                        Actions = item.Actions,
                        Children = filteredChildren ?? new List<DaisyTreeData>()
                    };
                    yield return filteredItem;
                }
            }
        }
        
        private int CountAllItems(IEnumerable<DaisyTreeData> items)
        {
            int count = 0;
            foreach (var item in items)
            {
                count++;
                if (item.Children != null)
                    count += CountAllItems(item.Children);
            }
            return count;
        }
    }
    
    #endregion
    
    #region 配置类
    
    /// <summary>
    /// 树搜索配置
    /// </summary>
    public class TreeSearchConfig
    {
        public bool SearchInText { get; set; } = true;
        public bool SearchInIcon { get; set; } = true;
        public bool AutoExpandMatches { get; set; } = true;
        public int MinQueryLength { get; set; } = 2;
        public int DebounceDelayMs { get; set; } = 300;
        public bool CaseSensitive { get; set; } = false;
        public bool UseRegex { get; set; } = false;
    }
    
    #endregion
    
    #region 新版DaisyTree实现
    
    /// <summary>
    /// 使用新事件系统的DaisyTree实现
    /// </summary>
    public partial class NewDaisyTree : VisualElement
    {
        private readonly IEventBus _eventBus;
        private readonly IEventSubscriptionManager _subscriptionManager;
        private readonly string _componentId;
        private readonly TreeSearchConfig _searchConfig;
        
        private DaisyInput _searchInput;
        private TreeView _treeView;
        private List<DaisyTreeData> _originalTreeData;
        private List<DaisyTreeData> _filteredTreeData;
        private VisualElement _emptyStateElement;
        
        public string ComponentId => _componentId;
        public List<DaisyTreeData> TreeData => _originalTreeData;
        public List<DaisyTreeData> FilteredData => _filteredTreeData;
        public TreeSearchConfig SearchConfig => _searchConfig;
        
        public NewDaisyTree(IEventBus eventBus, string componentId = null, TreeSearchConfig searchConfig = null)
        {
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
            _componentId = componentId ?? Guid.NewGuid().ToString();
            _searchConfig = searchConfig ?? new TreeSearchConfig();
            
            // 创建订阅管理器
            _subscriptionManager = new EventSubscriptionManager(_eventBus);
            
            // 订阅事件
            SetupEventSubscriptions();
            
            // 初始化组件
            InitializeComponent();
        }
        
        private void SetupEventSubscriptions()
        {
            // 订阅过滤结果事件 - 只处理本组件的事件
            _subscriptionManager.Subscribe<TreeDataFilterEvent>(
                HandleFilteredData, 
                evt => evt.ComponentId == _componentId
            );
            
            // 订阅显示更新事件
            _subscriptionManager.Subscribe<TreeDisplayUpdateEvent>(
                HandleDisplayUpdate,
                evt => evt.ComponentId == _componentId
            );
            
            // 订阅性能监控事件（可选）
            if (_searchConfig.EnablePerformanceMonitoring)
            {
                _subscriptionManager.Subscribe<SearchPerformanceEvent>(
                    HandlePerformanceUpdate,
                    evt => evt.ComponentId == _componentId
                );
            }
        }
        
        private void InitializeComponent()
        {
            // 创建搜索输入框
            _searchInput = new DaisyInput();
            _searchInput.SetPlaceholder("搜索...");
            _searchInput.OnValueChanged += OnSearchInputChanged;
            
            // 创建树视图
            _treeView = new TreeView();
            
            // 创建空状态元素
            _emptyStateElement = new VisualElement();
            _emptyStateElement.Add(new Label("没有找到匹配的项目"));
            _emptyStateElement.style.display = DisplayStyle.None;
            
            // 添加到UI
            Add(_searchInput);
            Add(_treeView);
            Add(_emptyStateElement);
        }
        
        private void OnSearchInputChanged(string newValue)
        {
            // 发布搜索事件
            _eventBus.Publish(new TreeSearchEvent(
                _componentId,
                newValue,
                _searchConfig.MinQueryLength,
                true // 实时搜索
            ));
        }
        
        private void HandleFilteredData(TreeDataFilterEvent filterEvent)
        {
            _filteredTreeData = filterEvent.FilteredData.ToList();
            
            // 更新TreeView
            var itemDataList = _filteredTreeData.ConvertAll(item => CreateTreeViewItemData(item));
            _treeView.SetRootItems(itemDataList);
            
            // 发布显示更新事件
            _eventBus.Publish(new TreeDisplayUpdateEvent(
                _componentId,
                _filteredTreeData.Count == 0 && !string.IsNullOrWhiteSpace(filterEvent.SearchQuery),
                _filteredTreeData.Count,
                filterEvent.SearchQuery
            ));
        }
        
        private void HandleDisplayUpdate(TreeDisplayUpdateEvent displayEvent)
        {
            // 更新空状态显示
            _emptyStateElement.style.display = displayEvent.IsEmpty ? DisplayStyle.Flex : DisplayStyle.None;
            _treeView.style.display = displayEvent.IsEmpty ? DisplayStyle.None : DisplayStyle.Flex;
            
            // 可以在这里添加其他UI更新逻辑
            Debug.Log($"Tree display updated: {displayEvent.VisibleItemCount} items visible");
        }
        
        private void HandlePerformanceUpdate(SearchPerformanceEvent performanceEvent)
        {
            // 性能监控逻辑
            Debug.Log($"Search performance: {performanceEvent.SearchDuration.TotalMilliseconds:F2}ms, " +
                     $"searched {performanceEvent.TotalItemsSearched} items, " +
                     $"found {performanceEvent.ResultCount} results");
        }
        
        private TreeViewItemData<DaisyTreeData> CreateTreeViewItemData(DaisyTreeData data)
        {
            var children = new List<TreeViewItemData<DaisyTreeData>>();
            
            // 递归创建子项
            foreach (var child in data.Children)
            {
                children.Add(CreateTreeViewItemData(child));
            }
            
            return new TreeViewItemData<DaisyTreeData>(data.Id.GetHashCode(), data, children);
        }
        
        public void SetTreeData(List<DaisyTreeData> treeData)
        {
            _originalTreeData = treeData ?? new List<DaisyTreeData>();
            _filteredTreeData = new List<DaisyTreeData>(_originalTreeData);
            
            // 注册到搜索事件处理器（这里简化处理）
            // 实际应用中需要通过依赖注入获取TreeSearchEventHandler
            
            // 初始显示
            var itemDataList = _filteredTreeData.ConvertAll(item => CreateTreeViewItemData(item));
            _treeView.SetRootItems(itemDataList);
        }
        
        public void ProgrammaticSearch(string query)
        {
            _searchInput.SetValue(query);
            
            // 发布搜索事件
            _eventBus.Publish(new TreeSearchEvent(
                _componentId,
                query,
                0, // 程序化搜索不受最小长度限制
                false // 非实时搜索
            ));
        }
        
        public void ClearSearch()
        {
            _searchInput.SetValue(string.Empty);
            _eventBus.Publish(new TreeSearchEvent(_componentId, string.Empty));
        }
        
        // 清理资源
        public void Dispose()
        {
            _subscriptionManager?.Dispose();
        }
    }
    
    #endregion
    
    #region 事件订阅管理器实现
    
    /// <summary>
    /// 事件订阅管理器实现
    /// </summary>
    public class EventSubscriptionManager : IEventSubscriptionManager
    {
        private readonly IEventBus _eventBus;
        private readonly List<IEventSubscription> _subscriptions;
        
        public EventSubscriptionManager(IEventBus eventBus)
        {
            _eventBus = eventBus;
            _subscriptions = new List<IEventSubscription>();
        }
        
        public void Subscribe<T>(Action<T> handler) where T : IEvent
        {
            _eventBus.Subscribe(handler);
            _subscriptions.Add(new EventSubscription<T>(handler));
        }
        
        public void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            _eventBus.Subscribe(handler, filter);
            _subscriptions.Add(new EventSubscription<T>(handler, filter));
        }
        
        public void Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            _eventBus.Subscribe(handler, priority);
            _subscriptions.Add(new EventSubscription<T>(handler, priority));
        }
        
        public void UnsubscribeAll()
        {
            foreach (var subscription in _subscriptions)
            {
                subscription.Unsubscribe(_eventBus);
            }
            _subscriptions.Clear();
        }
        
        public void Dispose()
        {
            UnsubscribeAll();
        }
    }
    
    /// <summary>
    /// 事件订阅接口
    /// </summary>
    public interface IEventSubscription
    {
        void Unsubscribe(IEventBus eventBus);
    }
    
    /// <summary>
    /// 事件订阅实现
    /// </summary>
    public class EventSubscription<T> : IEventSubscription where T : IEvent
    {
        private readonly Action<T> _handler;
        private readonly Func<T, bool> _filter;
        private readonly int _priority;
        
        public EventSubscription(Action<T> handler, Func<T, bool> filter = null, int priority = 0)
        {
            _handler = handler;
            _filter = filter;
            _priority = priority;
        }
        
        public void Unsubscribe(IEventBus eventBus)
        {
            eventBus.Unsubscribe(_handler);
        }
    }
    
    #endregion
}

// 扩展配置
namespace BlastingDesign.EventSystem.Examples
{
    public partial class TreeSearchConfig
    {
        public bool EnablePerformanceMonitoring { get; set; } = false;
        public bool EnableSearchHistory { get; set; } = false;
        public int MaxSearchHistory { get; set; } = 10;
        public bool EnableSearchSuggestions { get; set; } = false;
    }
}