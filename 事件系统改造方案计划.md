# 事件系统改造方案计划

## 1. 项目概述

### 1.1 改造目标
本项目旨在升级现有的事件系统，构建一个更加高效、灵活、可维护的通用事件通信架构，支持：
- UI组件间的高效通信
- 三维场景与UI间的无缝数据交换
- 任意对象间的松耦合通信
- 完善的事件订阅和取消机制
- 优秀的性能表现和开发体验

### 1.2 改造原则
- **向后兼容**：保持现有API的兼容性，确保平滑迁移
- **性能优先**：解决现有系统的性能瓶颈
- **架构简化**：降低系统复杂性，提高可维护性
- **扩展性增强**：支持更多定制化需求
- **开发体验**：提供更好的调试工具和开发支持

## 2. 现有系统分析

### 2.1 核心架构总结
现有事件系统采用事件驱动架构，包含以下核心组件：

| 组件 | 功能 | 优点 | 缺点 |
|------|------|------|------|
| UIEventSystem | 事件中心，单例管理 | 集中管理，API简洁 | 性能开销大，全局状态 |
| InputEventPriorityManager | 事件优先级管理 | 解决UI/场景冲突 | 复杂度高 |
| UIEventBlocker | 事件阻断机制 | 精确控制事件传播 | 静态设计，扩展性有限 |
| EventSystemManager | Unity EventSystem管理 | 良好的Unity集成 | 紧耦合 |
| SelectionManager | 对象选择管理 | 完整的选择机制 | 与输入系统耦合 |

### 2.2 现有系统优势
1. **功能完整**：涵盖UI、输入、选择、回调等全面功能
2. **冲突解决**：有效解决UI与场景交互冲突
3. **扩展性**：支持自定义事件和组件扩展
4. **调试支持**：提供调试日志和测试工具
5. **生产级稳定性**：已在生产环境验证

### 2.3 主要问题
1. **性能问题**：
   - UnityEvent性能开销大（装箱/拆箱、反射）
   - 频繁事件触发导致GC压力
   - 字符串比较回调系统效率低

2. **架构复杂性**：
   - 多层事件系统调试困难
   - 单例模式导致全局状态管理复杂
   - 事件流向追踪困难

3. **扩展性限制**：
   - 事件类型需预先定义
   - 缺乏动态事件支持
   - 无条件订阅机制

4. **内存管理**：
   - 手动监听器清理容易泄漏
   - 静态系统持有过多引用
   - 缺乏自动生命周期管理

## 3. 新架构设计方案

### 3.1 分层架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    事件服务层 (Event Service Layer)               │
│  EventDebugger | EventLogger | EventPersistence | EventMetrics │
├─────────────────────────────────────────────────────────────┤
│                 专用事件处理器 (Specialized Event Handlers)         │
│  UIEventHandler | InputEventHandler | SelectionEventHandler     │
├─────────────────────────────────────────────────────────────┤
│                  事件管理层 (Event Management Layer)              │
│  EventTypeRegistry | EventFilterManager | EventPriorityManager │
├─────────────────────────────────────────────────────────────┤
│                   核心事件引擎 (Event Engine Core)                │
│    IEventBus | EventBus | EventPool | EventScheduler          │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心组件详细设计

#### 3.2.1 事件引擎核心层

**IEventBus接口**
```csharp
public interface IEventBus
{
    // 基础订阅/取消订阅
    void Subscribe<T>(Action<T> handler) where T : IEvent;
    void Unsubscribe<T>(Action<T> handler) where T : IEvent;
    
    // 条件订阅
    void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent;
    
    // 优先级订阅
    void Subscribe<T>(Action<T> handler, int priority) where T : IEvent;
    
    // 事件发布
    void Publish<T>(T eventData) where T : IEvent;
    void PublishAsync<T>(T eventData) where T : IEvent;
    
    // 延迟发布
    void PublishDelayed<T>(T eventData, float delay) where T : IEvent;
    
    // 批量发布
    void PublishBatch<T>(IEnumerable<T> events) where T : IEvent;
}
```

**EventBus实现**
```csharp
public class EventBus : IEventBus
{
    private readonly Dictionary<Type, List<EventSubscription>> _subscriptions;
    private readonly EventPool _eventPool;
    private readonly EventScheduler _scheduler;
    
    // 高性能事件分发实现
    public void Publish<T>(T eventData) where T : IEvent
    {
        // 使用Action/Func替代UnityEvent
        // 支持事件池化
        // 实现批处理机制
    }
}
```

#### 3.2.2 事件管理层

**EventTypeRegistry - 动态事件类型注册**
```csharp
public class EventTypeRegistry
{
    public void RegisterEventType<T>() where T : IEvent;
    public void RegisterEventType(Type eventType);
    public bool IsEventTypeRegistered(Type eventType);
    public IEnumerable<Type> GetAllEventTypes();
}
```

**EventFilterManager - 事件过滤管理**
```csharp
public class EventFilterManager
{
    public void AddFilter<T>(Func<T, bool> filter) where T : IEvent;
    public void RemoveFilter<T>(Func<T, bool> filter) where T : IEvent;
    public bool ShouldProcess<T>(T eventData) where T : IEvent;
}
```

#### 3.2.3 专用事件处理器

**UIEventHandler - UI事件处理器**
```csharp
public class UIEventHandler : IEventHandler
{
    private readonly IEventBus _eventBus;
    private readonly UIEventSystemAdapter _adapter; // 向后兼容
    
    // 处理工具栏、面板、选择等UI事件
    public void HandleToolbarEvent(ToolbarEvent evt);
    public void HandlePanelEvent(PanelEvent evt);
    public void HandleSelectionEvent(SelectionEvent evt);
}
```

### 3.3 性能优化策略

#### 3.3.1 事件池化机制
```csharp
public class EventPool
{
    private readonly Dictionary<Type, Queue<IEvent>> _pools;
    
    public T Get<T>() where T : IEvent, new();
    public void Return<T>(T eventData) where T : IEvent;
    public void Clear();
}
```

#### 3.3.2 批处理和延迟处理
```csharp
public class EventScheduler
{
    public void ScheduleEvent<T>(T eventData, float delay) where T : IEvent;
    public void ScheduleBatch<T>(IEnumerable<T> events) where T : IEvent;
    public void ProcessScheduledEvents();
}
```

#### 3.3.3 内存管理优化
```csharp
public class EventSubscription : IDisposable
{
    private WeakReference _targetRef;
    private readonly Action<IEvent> _handler;
    
    public void Dispose()
    {
        // 自动清理监听器
    }
}
```

### 3.4 向后兼容策略

#### 3.4.1 适配器模式
```csharp
public class UIEventSystemAdapter : IEventBus
{
    private readonly UIEventSystem _legacySystem;
    private readonly IEventBus _newSystem;
    
    public void Subscribe<T>(Action<T> handler) where T : IEvent
    {
        // 根据事件类型路由到新旧系统
        if (IsLegacyEventType<T>())
        {
            SubscribeToLegacySystem(handler);
        }
        else
        {
            _newSystem.Subscribe(handler);
        }
    }
}
```

#### 3.4.2 渐进式迁移支持
```csharp
[System.Obsolete("Use IEventBus.Subscribe instead")]
public static class UIEventSystemCompat
{
    public static void TriggerMenuItemClicked(string menuItem)
    {
        // 兼容现有API，内部使用新系统
        EventBus.Instance.Publish(new MenuItemClickedEvent(menuItem));
    }
}
```

## 4. 实施计划

### 4.1 分阶段实施策略

#### 阶段1：基础架构搭建（2-3周）
**目标**：建立新事件系统的核心基础

**任务清单**：
- [ ] 实现`IEventBus`接口和`EventBus`核心类
- [ ] 创建`EventPool`对象池系统
- [ ] 实现`EventScheduler`调度器
- [ ] 建立基础的事件类型定义
- [ ] 创建`UIEventSystemAdapter`适配器
- [ ] 编写基础单元测试

**交付物**：
- 核心事件系统类库
- 适配器组件
- 基础测试套件
- 技术文档

#### 阶段2：性能优化（2-3周）
**目标**：解决现有系统的性能瓶颈

**任务清单**：
- [ ] 实现高频事件的Action/Func替换
- [ ] 优化字符串比较的回调系统
- [ ] 实现事件批处理机制
- [ ] 添加性能监控工具
- [ ] 内存泄漏检测和优化
- [ ] 性能基准测试

**交付物**：
- 性能优化的事件系统
- 性能监控工具
- 性能测试报告
- 优化指南文档

#### 阶段3：功能增强（3-4周）
**目标**：实现新的高级功能

**任务清单**：
- [ ] 实现`EventTypeRegistry`动态事件类型系统
- [ ] 开发`EventFilterManager`过滤机制
- [ ] 集成现有的`InputEventPriorityManager`
- [ ] 实现自动化监听器生命周期管理
- [ ] 开发事件条件订阅功能
- [ ] 实现事件优先级系统

**交付物**：
- 完整的事件管理系统
- 高级功能组件
- 集成测试套件
- 用户使用指南

#### 阶段4：工具和调试（2-3周）
**目标**：提供完善的开发和调试支持

**任务清单**：
- [ ] 开发事件流可视化工具
- [ ] 实现事件重放和回溯功能
- [ ] 创建内存泄漏检测工具
- [ ] 开发性能分析面板
- [ ] 实现事件日志系统
- [ ] 创建迁移辅助工具

**交付物**：
- 事件调试工具套件
- 开发者工具面板
- 迁移工具
- 完整的文档体系

### 4.2 时间表

| 阶段 | 时间 | 里程碑 |
|------|------|---------|
| 阶段1 | 第1-3周 | 核心架构完成 |
| 阶段2 | 第4-6周 | 性能优化完成 |
| 阶段3 | 第7-10周 | 功能增强完成 |
| 阶段4 | 第11-13周 | 工具开发完成 |
| 总计 | 13周 | 完整系统交付 |

## 5. 风险评估和应对策略

### 5.1 技术风险

#### 风险1：现有系统集成复杂度
**风险等级**：中等
**影响**：可能导致迁移时间延长
**应对策略**：
- 详细分析现有系统依赖关系
- 创建完整的适配器层
- 分模块渐进式迁移

#### 风险2：性能回归
**风险等级**：中等
**影响**：新系统性能不如预期
**应对策略**：
- 建立性能基准测试
- 持续性能监控
- 逐步优化策略

#### 风险3：内存管理复杂性
**风险等级**：高
**影响**：可能引入新的内存泄漏
**应对策略**：
- 使用WeakReference设计
- 实现自动清理机制
- 增加内存监控工具

### 5.2 项目风险

#### 风险1：开发周期延长
**风险等级**：中等
**影响**：项目交付时间延后
**应对策略**：
- 采用敏捷开发方法
- 分阶段交付
- 并行开发策略

#### 风险2：团队学习成本
**风险等级**：低
**影响**：团队需要学习新API
**应对策略**：
- 提供完整的迁移指南
- 保持API兼容性
- 提供培训和支持

## 6. 技术规范

### 6.1 编码规范

#### 6.1.1 命名约定
- 事件类：使用`Event`后缀，如`MenuItemClickedEvent`
- 接口：使用`I`前缀，如`IEventBus`
- 事件处理器：使用`Handler`后缀，如`UIEventHandler`

#### 6.1.2 代码结构
```
Assets/Scripts/Events/
├── Core/                  # 核心事件引擎
│   ├── IEventBus.cs
│   ├── EventBus.cs
│   ├── EventPool.cs
│   └── EventScheduler.cs
├── Management/            # 事件管理层
│   ├── EventTypeRegistry.cs
│   ├── EventFilterManager.cs
│   └── EventPriorityManager.cs
├── Handlers/              # 专用事件处理器
│   ├── UIEventHandler.cs
│   ├── InputEventHandler.cs
│   └── SelectionEventHandler.cs
├── Services/              # 事件服务层
│   ├── EventDebugger.cs
│   ├── EventLogger.cs
│   └── EventMetrics.cs
└── Compatibility/         # 向后兼容
    ├── UIEventSystemAdapter.cs
    └── LegacyEventSupport.cs
```

### 6.2 性能标准

#### 6.2.1 性能目标
- 事件发布延迟：< 0.1ms
- 内存分配：每帧 < 1KB
- GC压力：减少80%
- 事件处理吞吐量：> 10,000 events/second

#### 6.2.2 监控指标
- 事件发布频率
- 内存使用情况
- GC触发次数
- 事件处理延迟

### 6.3 测试规范

#### 6.3.1 单元测试
- 覆盖率要求：> 90%
- 测试框架：Unity Test Framework
- 模拟框架：Moq或NSubstitute

#### 6.3.2 集成测试
- UI事件流测试
- 性能压力测试
- 内存泄漏测试
- 兼容性测试

#### 6.3.3 性能测试
- 基准测试套件
- 压力测试场景
- 内存使用分析
- 性能回归检测

## 7. 迁移指南

### 7.1 API迁移映射

#### 7.1.1 现有API到新API的映射
```csharp
// 旧API
UIEventSystem.TriggerMenuItemClicked("File.New");

// 新API
eventBus.Publish(new MenuItemClickedEvent("File.New"));

// 兼容API（过渡期使用）
UIEventSystemCompat.TriggerMenuItemClicked("File.New");
```

#### 7.1.2 事件监听迁移
```csharp
// 旧方式
UIEventSystem.Instance.Toolbar.OnMenuItemClicked.AddListener(OnMenuItemClicked);

// 新方式
eventBus.Subscribe<MenuItemClickedEvent>(OnMenuItemClicked);

// 带条件的订阅
eventBus.Subscribe<MenuItemClickedEvent>(
    OnMenuItemClicked, 
    evt => evt.MenuItem.StartsWith("File.")
);
```

### 7.2 逐步迁移策略

#### 7.2.1 第一阶段：保持现有代码不变
- 部署新事件系统
- 启用适配器模式
- 验证现有功能正常

#### 7.2.2 第二阶段：逐步替换高频事件
- 识别高频触发的事件
- 优先迁移性能敏感的事件
- 验证性能改善

#### 7.2.3 第三阶段：迁移复杂事件逻辑
- 迁移复杂的事件处理逻辑
- 利用新系统的高级功能
- 简化事件流程

#### 7.2.4 第四阶段：清理旧代码
- 移除适配器层
- 清理旧API引用
- 完成完全迁移

### 7.3 迁移工具

#### 7.3.1 代码迁移工具
```csharp
public class EventSystemMigrationTool
{
    public void AnalyzeCurrentUsage();
    public void GenerateMigrationReport();
    public void AutoMigrateSimpleEvents();
    public void ValidateMigration();
}
```

#### 7.3.2 兼容性检查工具
```csharp
public class CompatibilityChecker
{
    public bool CheckAPICompatibility();
    public List<string> GetIncompatibleAPIs();
    public void GenerateCompatibilityReport();
}
```

## 8. 监控和维护

### 8.1 运行时监控

#### 8.1.1 性能监控
- 事件发布频率监控
- 内存使用情况跟踪
- GC触发频率分析
- 事件处理延迟统计

#### 8.1.2 事件流监控
- 事件发布者追踪
- 事件订阅者管理
- 事件传播路径分析
- 异常事件检测

### 8.2 调试工具

#### 8.2.1 事件流可视化
```csharp
public class EventFlowVisualizer
{
    public void ShowEventFlow();
    public void TrackEventPath(Type eventType);
    public void ShowSubscriberGraph();
    public void AnalyzeEventPerformance();
}
```

#### 8.2.2 内存泄漏检测
```csharp
public class EventMemoryProfiler
{
    public void StartProfiling();
    public void StopProfiling();
    public MemoryReport GenerateReport();
    public List<LeakSuspect> DetectLeaks();
}
```

### 8.3 维护建议

#### 8.3.1 定期维护任务
- 性能基准测试（月度）
- 内存泄漏检查（周度）
- 事件使用分析（季度）
- 系统健康检查（周度）

#### 8.3.2 持续改进
- 收集用户反馈
- 分析性能数据
- 优化热点代码
- 更新最佳实践

## 9. 成功指标

### 9.1 性能指标
- [ ] 事件发布延迟减少50%
- [ ] 内存分配减少80%
- [ ] GC压力降低70%
- [ ] 事件处理吞吐量提升100%

### 9.2 开发体验指标
- [ ] 事件调试时间减少60%
- [ ] 新功能开发效率提升40%
- [ ] 内存泄漏事件减少90%
- [ ] 代码可维护性评分提升30%

### 9.3 系统稳定性指标
- [ ] 事件相关崩溃减少95%
- [ ] 系统响应时间改善30%
- [ ] 用户界面流畅度提升20%
- [ ] 整体系统稳定性提升25%

## 10. 总结

本改造方案旨在在保持现有系统稳定性的前提下，逐步提升事件系统的性能、可维护性和扩展性。通过分阶段实施、向后兼容和完善的监控工具，确保改造过程平稳进行，最终交付一个现代化的高性能事件系统。

改造完成后，系统将具备：
- 更高的性能表现
- 更好的开发体验
- 更强的扩展能力
- 更完善的调试工具
- 更稳定的运行环境

这将为后续的功能开发和系统维护提供坚实的基础。