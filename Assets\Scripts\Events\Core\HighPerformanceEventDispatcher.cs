using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 高性能事件分发器
    /// 专门用于处理高频事件，避免装箱拆箱和反射开销
    /// </summary>
    public class HighPerformanceEventDispatcher : IDisposable
    {
        // 使用Action委托替代UnityEvent，避免装箱拆箱
        private readonly Dictionary<Type, List<Delegate>> _fastHandlers;
        private readonly Dictionary<Type, List<FilteredHandler>> _filteredHandlers;
        private readonly object _lockObject = new object();
        
        // 性能优化：预分配的临时列表
        private readonly List<Delegate> _tempHandlerList = new List<Delegate>(32);
        private readonly List<FilteredHandler> _tempFilteredList = new List<FilteredHandler>(32);
        
        private readonly bool _enableDebugLogging;
        
        public HighPerformanceEventDispatcher(bool enableDebugLogging = false)
        {
            _fastHandlers = new Dictionary<Type, List<Delegate>>();
            _filteredHandlers = new Dictionary<Type, List<FilteredHandler>>();
            _enableDebugLogging = enableDebugLogging;
        }
        
        /// <summary>
        /// 快速订阅事件（无过滤器）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Subscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null) return;
            
            Type eventType = typeof(T);
            
            lock (_lockObject)
            {
                if (!_fastHandlers.TryGetValue(eventType, out List<Delegate> handlers))
                {
                    handlers = new List<Delegate>();
                    _fastHandlers[eventType] = handlers;
                }
                
                handlers.Add(handler);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"HighPerformanceEventDispatcher: 快速订阅事件 {eventType.Name}");
                }
            }
        }
        
        /// <summary>
        /// 带过滤器的订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="filter">过滤器</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            if (handler == null) return;
            
            Type eventType = typeof(T);
            var filteredHandler = new FilteredHandler<T>(handler, filter);
            
            lock (_lockObject)
            {
                if (!_filteredHandlers.TryGetValue(eventType, out List<FilteredHandler> handlers))
                {
                    handlers = new List<FilteredHandler>();
                    _filteredHandlers[eventType] = handlers;
                }
                
                handlers.Add(filteredHandler);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"HighPerformanceEventDispatcher: 带过滤器订阅事件 {eventType.Name}");
                }
            }
        }
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null) return;
            
            Type eventType = typeof(T);
            
            lock (_lockObject)
            {
                // 从快速处理器中移除
                if (_fastHandlers.TryGetValue(eventType, out List<Delegate> fastHandlers))
                {
                    fastHandlers.Remove(handler);
                }
                
                // 从过滤处理器中移除
                if (_filteredHandlers.TryGetValue(eventType, out List<FilteredHandler> filteredHandlers))
                {
                    for (int i = filteredHandlers.Count - 1; i >= 0; i--)
                    {
                        if (filteredHandlers[i] is FilteredHandler<T> typedHandler && 
                            typedHandler.Handler.Equals(handler))
                        {
                            filteredHandlers.RemoveAt(i);
                            break;
                        }
                    }
                }
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"HighPerformanceEventDispatcher: 取消订阅事件 {eventType.Name}");
                }
            }
        }
        
        /// <summary>
        /// 高性能事件分发
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Dispatch<T>(T eventData) where T : IEvent
        {
            if (eventData == null) return;
            
            Type eventType = typeof(T);
            
            // 处理快速处理器（无锁优化）
            DispatchFastHandlers(eventType, eventData);
            
            // 处理过滤处理器
            DispatchFilteredHandlers(eventType, eventData);
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void DispatchFastHandlers<T>(Type eventType, T eventData) where T : IEvent
        {
            if (!_fastHandlers.TryGetValue(eventType, out List<Delegate> handlers) || handlers.Count == 0)
                return;
            
            // 复制处理器列表以避免锁定
            _tempHandlerList.Clear();
            lock (_lockObject)
            {
                _tempHandlerList.AddRange(handlers);
            }
            
            // 快速分发，避免装箱
            for (int i = 0; i < _tempHandlerList.Count; i++)
            {
                if (_tempHandlerList[i] is Action<T> typedHandler)
                {
                    try
                    {
                        typedHandler(eventData);
                        
                        if (eventData.IsCancelled)
                            break;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"HighPerformanceEventDispatcher: 快速处理器执行错误 {eventType.Name}: {ex.Message}");
                    }
                }
            }
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void DispatchFilteredHandlers<T>(Type eventType, T eventData) where T : IEvent
        {
            if (!_filteredHandlers.TryGetValue(eventType, out List<FilteredHandler> handlers) || handlers.Count == 0)
                return;
            
            // 复制处理器列表以避免锁定
            _tempFilteredList.Clear();
            lock (_lockObject)
            {
                _tempFilteredList.AddRange(handlers);
            }
            
            // 分发过滤处理器
            for (int i = 0; i < _tempFilteredList.Count; i++)
            {
                if (_tempFilteredList[i] is FilteredHandler<T> typedHandler)
                {
                    try
                    {
                        typedHandler.Invoke(eventData);
                        
                        if (eventData.IsCancelled)
                            break;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"HighPerformanceEventDispatcher: 过滤处理器执行错误 {eventType.Name}: {ex.Message}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 获取订阅者数量
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>订阅者数量</returns>
        public int GetSubscriberCount<T>() where T : IEvent
        {
            Type eventType = typeof(T);
            int count = 0;
            
            lock (_lockObject)
            {
                if (_fastHandlers.TryGetValue(eventType, out List<Delegate> fastHandlers))
                {
                    count += fastHandlers.Count;
                }
                
                if (_filteredHandlers.TryGetValue(eventType, out List<FilteredHandler> filteredHandlers))
                {
                    count += filteredHandlers.Count;
                }
            }
            
            return count;
        }
        
        /// <summary>
        /// 清除所有订阅
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                _fastHandlers.Clear();
                _filteredHandlers.Clear();
                
                if (_enableDebugLogging)
                {
                    Debug.Log("HighPerformanceEventDispatcher: 清除所有订阅");
                }
            }
        }
        
        public void Dispose()
        {
            ClearAll();
        }
    }
    
    /// <summary>
    /// 过滤处理器基类
    /// </summary>
    internal abstract class FilteredHandler
    {
        public abstract void Invoke(IEvent eventData);
    }
    
    /// <summary>
    /// 泛型过滤处理器
    /// </summary>
    internal class FilteredHandler<T> : FilteredHandler where T : IEvent
    {
        public Action<T> Handler { get; private set; }
        public Func<T, bool> Filter { get; private set; }
        
        public FilteredHandler(Action<T> handler, Func<T, bool> filter)
        {
            Handler = handler;
            Filter = filter;
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void Invoke(IEvent eventData)
        {
            if (eventData is T typedEvent)
            {
                if (Filter == null || Filter(typedEvent))
                {
                    Handler(typedEvent);
                }
            }
        }
    }
}
