using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 统一的事件错误处理器实现
    /// </summary>
    public class EventErrorHandler : IEventErrorHandler
    {
        private readonly ConcurrentDictionary<Type, object> _recoveryStrategies;
        private readonly EventErrorStatistics _statistics;
        private readonly bool _debugMode;

        public EventErrorHandler(bool debugMode = false)
        {
            _recoveryStrategies = new ConcurrentDictionary<Type, object>();
            _statistics = new EventErrorStatistics();
            _debugMode = debugMode;
        }

        public async Task<EventErrorResult> HandleError<T>(T eventData, Exception exception, EventErrorContext context) where T : IEvent
        {
            var result = new EventErrorResult
            {
                IsHandled = false,
                ShouldRetry = false,
                ShouldStopProcessing = false,
                ErrorDescription = exception.Message
            };

            try
            {
                // 记录错误统计
                _statistics.RecordError(typeof(T), exception, context);

                if (_debugMode)
                {
                    Debug.LogError($"EventErrorHandler: 处理事件 {typeof(T).Name} 时发生错误: {exception.Message}");
                }

                // 尝试使用恢复策略
                if (_recoveryStrategies.TryGetValue(typeof(T), out var strategy))
                {
                    if (strategy is IEventErrorRecoveryStrategy<T> typedStrategy)
                    {
                        var recoveryResult = await typedStrategy.TryRecover(eventData, exception, context);

                        result.IsHandled = recoveryResult.IsRecovered;
                        result.ShouldRetry = recoveryResult.ShouldRetryEvent;
                        result.RetryDelay = recoveryResult.RetryDelay;
                        result.ErrorDescription = recoveryResult.RecoveryDescription ?? result.ErrorDescription;

                        if (recoveryResult.IsRecovered)
                        {
                            _statistics.RecordRecovery(typeof(T));
                        }

                        if (_debugMode)
                        {
                            Debug.Log($"EventErrorHandler: 恢复策略执行完成，恢复状态: {recoveryResult.IsRecovered}");
                        }
                    }
                }

                // 如果没有恢复策略或恢复失败，应用默认策略
                if (!result.IsHandled)
                {
                    result = await ApplyDefaultErrorHandling(eventData, exception, context);
                }
            }
            catch (Exception handlerException)
            {
                Debug.LogError($"EventErrorHandler: 错误处理器自身发生错误: {handlerException.Message}");
                result.ShouldStopProcessing = true;
            }

            return result;
        }

        public void RegisterRecoveryStrategy<T>(IEventErrorRecoveryStrategy<T> strategy) where T : IEvent
        {
            if (strategy == null)
                throw new ArgumentNullException(nameof(strategy));

            _recoveryStrategies.AddOrUpdate(typeof(T), strategy, (key, oldValue) => strategy);

            if (_debugMode)
            {
                Debug.Log($"EventErrorHandler: 注册恢复策略 {typeof(T).Name}");
            }
        }

        public void RemoveRecoveryStrategy<T>() where T : IEvent
        {
            _recoveryStrategies.TryRemove(typeof(T), out _);

            if (_debugMode)
            {
                Debug.Log($"EventErrorHandler: 移除恢复策略 {typeof(T).Name}");
            }
        }

        public IEventErrorStatistics GetErrorStatistics()
        {
            return _statistics;
        }

        private Task<EventErrorResult> ApplyDefaultErrorHandling<T>(T eventData, Exception exception, EventErrorContext context) where T : IEvent
        {
            var result = new EventErrorResult
            {
                IsHandled = true,
                ShouldRetry = false,
                ShouldStopProcessing = false,
                ErrorDescription = "应用默认错误处理策略"
            };

            // 根据异常类型应用不同的默认策略
            switch (exception)
            {
                case ArgumentNullException:
                case ArgumentException:
                    // 参数错误，不重试
                    result.ShouldRetry = false;
                    result.ShouldStopProcessing = false;
                    break;

                case InvalidOperationException:
                    // 操作无效，尝试重试
                    if (context.RetryCount < context.MaxRetries)
                    {
                        result.ShouldRetry = true;
                        result.RetryDelay = TimeSpan.FromMilliseconds(100 * (context.RetryCount + 1));
                    }
                    break;

                case TimeoutException:
                    // 超时，尝试重试
                    if (context.RetryCount < context.MaxRetries)
                    {
                        result.ShouldRetry = true;
                        result.RetryDelay = TimeSpan.FromMilliseconds(500 * (context.RetryCount + 1));
                    }
                    break;

                case OutOfMemoryException:
                    // 内存不足，停止处理
                    result.ShouldStopProcessing = true;
                    break;

                default:
                    // 其他异常，根据重试次数决定
                    if (context.RetryCount < context.MaxRetries)
                    {
                        result.ShouldRetry = true;
                        result.RetryDelay = TimeSpan.FromMilliseconds(200 * (context.RetryCount + 1));
                    }
                    break;
            }

            return Task.FromResult(result);
        }
    }

    /// <summary>
    /// 默认的重试恢复策略
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    public class DefaultRetryRecoveryStrategy<T> : IEventErrorRecoveryStrategy<T> where T : IEvent
    {
        private readonly int _maxRetries;
        private readonly TimeSpan _baseDelay;
        private readonly bool _exponentialBackoff;

        public DefaultRetryRecoveryStrategy(int maxRetries = 3, TimeSpan? baseDelay = null, bool exponentialBackoff = true)
        {
            _maxRetries = maxRetries;
            _baseDelay = baseDelay ?? TimeSpan.FromMilliseconds(100);
            _exponentialBackoff = exponentialBackoff;
        }

        public Task<EventErrorRecoveryResult> TryRecover(T eventData, Exception exception, EventErrorContext context)
        {
            var result = new EventErrorRecoveryResult
            {
                IsRecovered = false,
                ShouldRetryEvent = false,
                RecoveryDescription = $"默认重试策略: 重试次数 {context.RetryCount}/{_maxRetries}"
            };

            if (context.RetryCount < _maxRetries)
            {
                result.ShouldRetryEvent = true;

                if (_exponentialBackoff)
                {
                    result.RetryDelay = TimeSpan.FromMilliseconds(_baseDelay.TotalMilliseconds * Math.Pow(2, context.RetryCount));
                }
                else
                {
                    result.RetryDelay = _baseDelay;
                }

                result.IsRecovered = true;
                result.RecoveryDescription = $"安排重试，延迟: {result.RetryDelay.TotalMilliseconds}ms";
            }
            else
            {
                result.RecoveryDescription = "已达到最大重试次数，停止重试";
            }

            return Task.FromResult(result);
        }
    }

    /// <summary>
    /// 事件错误统计信息实现
    /// </summary>
    internal class EventErrorStatistics : IEventErrorStatistics
    {
        private readonly ConcurrentDictionary<Type, EventTypeErrorStatistics> _typeStatistics;
        private readonly object _lockObject = new object();

        private long _totalErrors = 0;
        private long _handledErrors = 0;
        private long _unhandledErrors = 0;
        private long _recoveredErrors = 0;
        private long _totalEvents = 0;

        public long TotalErrors => _totalErrors;
        public long HandledErrors => _handledErrors;
        public long UnhandledErrors => _unhandledErrors;
        public long RecoveredErrors => _recoveredErrors;

        public double ErrorRate
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalEvents > 0 ? (double)_totalErrors / _totalEvents : 0;
                }
            }
        }

        public double RecoveryRate
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalErrors > 0 ? (double)_recoveredErrors / _totalErrors : 0;
                }
            }
        }

        public EventErrorStatistics()
        {
            _typeStatistics = new ConcurrentDictionary<Type, EventTypeErrorStatistics>();
        }

        public void RecordError(Type eventType, Exception exception, EventErrorContext context)
        {
            lock (_lockObject)
            {
                _totalErrors++;
                _totalEvents++;
            }

            var typeStats = _typeStatistics.GetOrAdd(eventType,
                type => new EventTypeErrorStatistics(type));
            typeStats.RecordError(exception, context);
        }

        public void RecordRecovery(Type eventType)
        {
            lock (_lockObject)
            {
                _recoveredErrors++;
                _handledErrors++;
            }

            if (_typeStatistics.TryGetValue(eventType, out var typeStats))
            {
                typeStats.RecordRecovery();
            }
        }

        public void RecordEvent(Type eventType)
        {
            lock (_lockObject)
            {
                _totalEvents++;
            }
        }

        public IEventTypeErrorStatistics GetEventTypeErrorStatistics(Type eventType)
        {
            return _typeStatistics.GetOrAdd(eventType,
                type => new EventTypeErrorStatistics(type));
        }
    }

    /// <summary>
    /// 事件类型错误统计信息实现
    /// </summary>
    internal class EventTypeErrorStatistics : IEventTypeErrorStatistics
    {
        private readonly object _lockObject = new object();
        private readonly List<string> _errorTypes = new List<string>();

        public Type EventType { get; }

        private long _errorCount = 0;
        private long _recoveryCount = 0;
        private DateTime _lastErrorTime = DateTime.MinValue;

        public long ErrorCount => _errorCount;
        public long RecoveryCount => _recoveryCount;
        public DateTime LastErrorTime => _lastErrorTime;

        public string[] CommonErrorTypes
        {
            get
            {
                lock (_lockObject)
                {
                    return _errorTypes.GroupBy(e => e)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .Select(g => g.Key)
                        .ToArray();
                }
            }
        }

        public EventTypeErrorStatistics(Type eventType)
        {
            EventType = eventType;
        }

        public void RecordError(Exception exception, EventErrorContext context)
        {
            lock (_lockObject)
            {
                _errorCount++;
                _lastErrorTime = context.ErrorTime;
                _errorTypes.Add(exception.GetType().Name);
            }
        }

        public void RecordRecovery()
        {
            lock (_lockObject)
            {
                _recoveryCount++;
            }
        }
    }
}