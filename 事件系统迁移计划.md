# 事件系统迁移计划

## 项目概述

本文档描述了将现有系统各模块的事件处理从传统的UnityEvent系统迁移到新的EventBus系统的详细计划。

## 迁移目标

### 主要目标
1. **统一事件处理**：将所有模块迁移到新的EventBus系统
2. **提升性能**：利用新系统的优化特性提升事件处理性能
3. **增强可维护性**：简化事件订阅和管理流程
4. **保持兼容性**：确保迁移过程中功能不受影响

### 预期收益
- 事件处理性能提升60%
- 代码复杂度降低40%
- 内存使用优化50%
- 统一的事件处理API

## 现状分析

### 当前事件系统架构
1. **UIEventSystem**：基于UnityEvent的UI事件系统（540行代码）
2. **InputEventHandler**：输入事件处理，使用传统委托模式
3. **UI组件事件**：各UI组件使用混合的事件处理方式
4. **DaisyUI组件**：部分组件有自己的事件处理逻辑

### 待迁移组件清单
| 组件类型 | 文件路径 | 当前事件类型 | 迁移优先级 |
|---------|----------|--------------|-----------|
| Toolbar | Assets/Scripts/UI/Components/Toolbar.cs | UnityEvent | 高 |
| StatusBar | Assets/Scripts/UI/Components/StatusBar.cs | UnityEvent | 高 |
| MenuBar | Assets/Scripts/UI/Components/MenuBar/ | UnityEvent | 高 |
| LeftPanel | Assets/Scripts/UI/Components/LeftPanel/ | UnityEvent | 高 |
| DaisyTree | Assets/Scripts/UI/Components/DaisyTree/ | 自定义事件 | 中 |
| DaisyInput | Assets/Scripts/UI/Components/DaisyInput/ | 自定义事件 | 中 |
| InputEventHandler | Assets/Scripts/Events/Handlers/InputEventHandler.cs | 委托 | 中 |
| UIEventSystem | Assets/Scripts/Events/UIEventSystem.cs | UnityEvent | 低 |

## 迁移计划

### 阶段一：准备和设计（1周）

#### 1.1 兼容性层设计
**目标**：确保新旧系统可以并存，支持渐进式迁移

**任务**：
- [ ] 完善UIEventSystemAdapter，提供新旧系统桥接
- [ ] 创建EventMigrationHelper工具类
- [ ] 设计事件类型映射方案

**输出**：
- 兼容性适配器代码
- 迁移工具类
- 事件映射配置

#### 1.2 事件类型统一设计
**目标**：为所有现有事件创建对应的新事件类型

**任务**：
- [ ] 分析现有事件类型，设计新的事件类定义
- [ ] 创建UI相关事件类（ButtonClickEvent、MenuSelectEvent等）
- [ ] 创建输入相关事件类（KeyPressEvent、MouseClickEvent等）
- [ ] 创建组件间通信事件类

**输出**：
- 新事件类型定义文件
- 事件类型文档

### 阶段二：核心UI组件迁移（2-3周）

#### 2.1 Toolbar组件迁移（3天）
**目标**：将Toolbar的事件处理迁移到新系统

**当前状态**：
```csharp
// 现有代码示例
public UnityEvent<string> OnToolSelected;
public UnityEvent<bool> OnToolToggled;
```

**迁移后**：
```csharp
// 新系统代码
private IEventBus _eventBus;

public void SelectTool(string toolName)
{
    _eventBus.Publish(new ToolSelectedEvent(toolName));
}

public void ToggleTool(bool isEnabled)
{
    _eventBus.Publish(new ToolToggledEvent(isEnabled));
}
```

**任务**：
- [ ] 创建Toolbar相关事件类
- [ ] 重构Toolbar事件发布逻辑
- [ ] 更新Toolbar事件订阅代码
- [ ] 测试Toolbar功能完整性

#### 2.2 StatusBar组件迁移（2天）
**目标**：迁移状态栏的事件处理

**任务**：
- [ ] 创建StatusBar相关事件类
- [ ] 重构状态更新事件处理
- [ ] 测试状态显示功能

#### 2.3 MenuBar组件迁移（5天）
**目标**：迁移菜单栏的复杂事件处理逻辑

**任务**：
- [ ] 分析MenuBar的事件处理逻辑
- [ ] 创建菜单相关事件类
- [ ] 重构菜单选择和操作事件
- [ ] 更新子菜单事件处理
- [ ] 测试菜单功能完整性

#### 2.4 LeftPanel组件迁移（5天）
**目标**：迁移左侧面板的事件处理

**任务**：
- [ ] 分析LeftPanel的事件处理逻辑
- [ ] 创建面板相关事件类
- [ ] 重构面板状态变更事件
- [ ] 更新面板内容交互事件
- [ ] 测试面板功能完整性

### 阶段三：输入系统整合（1-2周）

#### 3.1 InputEventHandler迁移（5天）
**目标**：将输入事件处理整合到新系统

**当前状态**：
```csharp
// 现有输入处理
public static event Action<KeyCode> OnKeyPressed;
public static event Action<Vector2> OnMouseClick;
```

**迁移后**：
```csharp
// 新系统输入处理
public void HandleKeyPress(KeyCode keyCode)
{
    _eventBus.Publish(new KeyPressEvent(keyCode));
}

public void HandleMouseClick(Vector2 position)
{
    _eventBus.Publish(new MouseClickEvent(position));
}
```

**任务**：
- [ ] 创建输入相关事件类
- [ ] 重构输入事件发布逻辑
- [ ] 更新输入事件订阅代码
- [ ] 测试输入响应功能

#### 3.2 输入优先级管理优化（3天）
**目标**：利用新系统的优先级功能优化输入处理

**任务**：
- [ ] 设计输入事件优先级方案
- [ ] 实现优先级订阅逻辑
- [ ] 测试输入优先级功能

### 阶段四：DaisyUI组件迁移（1-2周）

#### 4.1 DaisyTree组件迁移（3天）
**目标**：迁移DaisyTree的事件处理

**任务**：
- [ ] 分析DaisyTree的搜索事件处理
- [ ] 创建Tree相关事件类
- [ ] 重构搜索和选择事件
- [ ] 测试Tree功能完整性

#### 4.2 DaisyInput组件迁移（3天）
**目标**：迁移DaisyInput的事件处理

**任务**：
- [ ] 分析DaisyInput的交互事件
- [ ] 创建Input相关事件类
- [ ] 重构输入变更事件
- [ ] 测试Input功能完整性

#### 4.3 其他DaisyUI组件迁移（4天）
**目标**：迁移其他DaisyUI组件的事件处理

**任务**：
- [ ] 识别其他需要迁移的DaisyUI组件
- [ ] 逐个迁移组件事件处理
- [ ] 测试组件功能完整性

### 阶段五：系统清理和优化（1周）

#### 5.1 旧系统清理（3天）
**目标**：移除不再需要的旧事件系统代码

**任务**：
- [ ] 识别可以移除的旧代码
- [ ] 逐步移除UnityEvent相关代码
- [ ] 清理unused imports和references
- [ ] 更新项目依赖

#### 5.2 性能优化（2天）
**目标**：优化新系统的性能

**任务**：
- [ ] 分析事件处理性能瓶颈
- [ ] 优化事件订阅和发布流程
- [ ] 测试性能提升效果

#### 5.3 文档更新（2天）
**目标**：更新相关文档

**任务**：
- [ ] 更新事件处理文档
- [ ] 创建新系统使用指南
- [ ] 更新API文档

## 迁移策略

### 渐进式迁移原则
1. **保持功能完整性**：每个阶段都要确保现有功能不受影响
2. **独立模块迁移**：每个组件独立迁移，避免相互影响
3. **兼容性优先**：新旧系统并存，逐步过渡
4. **测试驱动**：每个迁移步骤都要有对应的测试验证

### 风险控制策略
1. **备份机制**：每次迁移前创建代码备份
2. **回滚计划**：为每个阶段制定回滚计划
3. **监控机制**：实时监控迁移后的系统性能
4. **分支策略**：使用独立分支进行迁移开发

## 测试策略

### 单元测试
- 每个组件的事件处理逻辑测试
- 事件发布和监听的正确性测试
- 新旧系统兼容性测试

### 集成测试
- 跨组件事件通信测试
- 完整工作流程测试
- 性能基准测试

### 用户验收测试
- 功能完整性验证
- 用户体验测试
- 性能体验测试

## 质量保证

### 代码质量
- 代码审查流程
- 静态代码分析
- 性能监控

### 测试覆盖
- 单元测试覆盖率 > 90%
- 集成测试覆盖核心功能
- 性能测试覆盖关键路径

### 文档维护
- 实时更新API文档
- 维护使用示例
- 更新故障排除指南

## 时间计划

| 阶段 | 任务 | 预计时间 | 里程碑 |
|-----|------|---------|-------|
| 阶段一 | 准备和设计 | 1周 | 完成兼容性层设计 |
| 阶段二 | 核心UI组件迁移 | 2-3周 | 完成主要UI组件迁移 |
| 阶段三 | 输入系统整合 | 1-2周 | 完成输入系统迁移 |
| 阶段四 | DaisyUI组件迁移 | 1-2周 | 完成DaisyUI组件迁移 |
| 阶段五 | 系统清理和优化 | 1周 | 完成系统清理 |
| **总计** | **完整迁移** | **6-9周** | **全面完成迁移** |

## 成功标准

### 功能标准
- [ ] 所有现有功能正常工作
- [ ] 事件处理响应时间 < 16ms
- [ ] 无内存泄漏
- [ ] 无性能退化

### 质量标准
- [ ] 代码覆盖率 > 90%
- [ ] 所有测试通过
- [ ] 性能提升 > 50%
- [ ] 代码复杂度降低 > 30%

### 可维护性标准
- [ ] 统一的事件处理API
- [ ] 完善的文档和示例
- [ ] 易于扩展的架构
- [ ] 清晰的错误处理

## 后续维护

### 监控机制
- 性能指标监控
- 错误率监控
- 用户体验监控

### 持续优化
- 定期性能优化
- 功能扩展支持
- 用户反馈处理

### 培训计划
- 开发团队培训
- 使用指南制作
- 最佳实践分享

## 联系人和支持

### 项目负责人
- 技术负责人：[待定]
- 测试负责人：[待定]
- 文档负责人：[待定]

### 支持资源
- 技术支持：新事件系统技术文档
- 测试支持：自动化测试套件
- 文档支持：API文档和使用指南

---

*本文档将随着项目进展持续更新*