using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Services
{
    /// <summary>
    /// 事件重放系统
    /// 提供事件记录、重放和回溯功能
    /// </summary>
    public class EventReplaySystem : IDisposable
    {
        private readonly List<EventReplayRecord> _recordedEvents;
        private readonly IEventBus _eventBus;
        private readonly object _lockObject = new object();
        
        // 配置
        private readonly int _maxRecordedEvents;
        private readonly bool _enableDebugLogging;
        
        // 重放状态
        private bool _isRecording = false;
        private bool _isReplaying = false;
        private int _currentReplayIndex = 0;
        private DateTime _recordingStartTime;
        private DateTime _replayStartTime;
        
        // 过滤设置
        private HashSet<Type> _recordedEventTypes;
        private HashSet<string> _recordedSources;
        private bool _useEventTypeFilter = false;
        private bool _useSourceFilter = false;
        
        public bool IsRecording => _isRecording;
        public bool IsReplaying => _isReplaying;
        public int RecordedEventCount => _recordedEvents.Count;
        public int CurrentReplayIndex => _currentReplayIndex;
        
        public EventReplaySystem(IEventBus eventBus, int maxRecordedEvents = 10000, bool enableDebugLogging = false)
        {
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
            _recordedEvents = new List<EventReplayRecord>();
            _maxRecordedEvents = maxRecordedEvents;
            _enableDebugLogging = enableDebugLogging;
            
            _recordedEventTypes = new HashSet<Type>();
            _recordedSources = new HashSet<string>();
        }
        
        /// <summary>
        /// 开始记录事件
        /// </summary>
        /// <param name="clearExisting">是否清除现有记录</param>
        public void StartRecording(bool clearExisting = true)
        {
            lock (_lockObject)
            {
                if (_isReplaying)
                {
                    Debug.LogWarning("EventReplaySystem: 无法在重放过程中开始记录");
                    return;
                }
                
                if (clearExisting)
                {
                    _recordedEvents.Clear();
                }
                
                _isRecording = true;
                _recordingStartTime = DateTime.UtcNow;
                
                if (_enableDebugLogging)
                {
                    Debug.Log("EventReplaySystem: 开始记录事件");
                }
            }
        }
        
        /// <summary>
        /// 停止记录事件
        /// </summary>
        public void StopRecording()
        {
            lock (_lockObject)
            {
                _isRecording = false;
                
                if (_enableDebugLogging)
                {
                    var duration = DateTime.UtcNow - _recordingStartTime;
                    Debug.Log($"EventReplaySystem: 停止记录事件，记录了 {_recordedEvents.Count} 个事件，持续时间: {duration.TotalSeconds:F2} 秒");
                }
            }
        }
        
        /// <summary>
        /// 记录事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <param name="subscriberCount">订阅者数量</param>
        public void RecordEvent(IEvent eventData, int subscriberCount)
        {
            if (!_isRecording || eventData == null) return;
            
            Type eventType = eventData.GetType();
            
            // 应用过滤器
            if (_useEventTypeFilter && !_recordedEventTypes.Contains(eventType))
            {
                return;
            }
            
            if (_useSourceFilter && !_recordedSources.Contains(eventData.Source))
            {
                return;
            }
            
            lock (_lockObject)
            {
                var record = new EventReplayRecord
                {
                    EventId = eventData.EventId,
                    EventType = eventType,
                    EventTypeName = eventType.Name,
                    Source = eventData.Source,
                    Timestamp = eventData.Timestamp,
                    RelativeTime = DateTime.UtcNow - _recordingStartTime,
                    SubscriberCount = subscriberCount,
                    EventData = CloneEvent(eventData) // 深拷贝事件数据
                };
                
                _recordedEvents.Add(record);
                
                // 限制记录数量
                if (_recordedEvents.Count > _maxRecordedEvents)
                {
                    _recordedEvents.RemoveAt(0);
                }
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventReplaySystem: 记录事件 {eventType.Name} (ID: {eventData.EventId})");
                }
            }
        }
        
        /// <summary>
        /// 开始重放事件
        /// </summary>
        /// <param name="startIndex">开始索引</param>
        /// <param name="endIndex">结束索引，-1表示到最后</param>
        /// <param name="timeScale">时间缩放，1.0表示原速度</param>
        public void StartReplay(int startIndex = 0, int endIndex = -1, float timeScale = 1.0f)
        {
            lock (_lockObject)
            {
                if (_isRecording)
                {
                    Debug.LogWarning("EventReplaySystem: 无法在记录过程中开始重放");
                    return;
                }
                
                if (_recordedEvents.Count == 0)
                {
                    Debug.LogWarning("EventReplaySystem: 没有记录的事件可以重放");
                    return;
                }
                
                _isReplaying = true;
                _currentReplayIndex = Math.Max(0, startIndex);
                _replayStartTime = DateTime.UtcNow;
                
                int actualEndIndex = endIndex == -1 ? _recordedEvents.Count - 1 : Math.Min(endIndex, _recordedEvents.Count - 1);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventReplaySystem: 开始重放事件，范围: {_currentReplayIndex} - {actualEndIndex}，时间缩放: {timeScale}");
                }
                
                // 启动重放协程
                StartReplayCoroutine(actualEndIndex, timeScale);
            }
        }
        
        /// <summary>
        /// 停止重放
        /// </summary>
        public void StopReplay()
        {
            lock (_lockObject)
            {
                _isReplaying = false;
                
                if (_enableDebugLogging)
                {
                    Debug.Log("EventReplaySystem: 停止重放事件");
                }
            }
        }
        
        /// <summary>
        /// 重放单个事件
        /// </summary>
        /// <param name="index">事件索引</param>
        public void ReplaySingleEvent(int index)
        {
            lock (_lockObject)
            {
                if (index < 0 || index >= _recordedEvents.Count)
                {
                    Debug.LogWarning($"EventReplaySystem: 无效的事件索引 {index}");
                    return;
                }
                
                var record = _recordedEvents[index];
                ReplayEventRecord(record);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventReplaySystem: 重放单个事件 {record.EventTypeName} (索引: {index})");
                }
            }
        }
        
        /// <summary>
        /// 获取记录的事件列表
        /// </summary>
        /// <param name="startIndex">开始索引</param>
        /// <param name="count">获取数量，-1表示全部</param>
        /// <returns>事件记录列表</returns>
        public List<EventReplayRecord> GetRecordedEvents(int startIndex = 0, int count = -1)
        {
            lock (_lockObject)
            {
                if (startIndex < 0 || startIndex >= _recordedEvents.Count)
                {
                    return new List<EventReplayRecord>();
                }
                
                if (count == -1)
                {
                    return _recordedEvents.Skip(startIndex).ToList();
                }
                
                return _recordedEvents.Skip(startIndex).Take(count).ToList();
            }
        }
        
        /// <summary>
        /// 根据事件类型获取记录
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件记录列表</returns>
        public List<EventReplayRecord> GetRecordedEventsByType(Type eventType)
        {
            lock (_lockObject)
            {
                return _recordedEvents.Where(record => record.EventType == eventType).ToList();
            }
        }
        
        /// <summary>
        /// 根据时间范围获取记录
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>事件记录列表</returns>
        public List<EventReplayRecord> GetRecordedEventsByTimeRange(DateTime startTime, DateTime endTime)
        {
            lock (_lockObject)
            {
                return _recordedEvents.Where(record => 
                    record.Timestamp >= startTime && record.Timestamp <= endTime).ToList();
            }
        }
        
        /// <summary>
        /// 设置记录的事件类型过滤器
        /// </summary>
        /// <param name="eventTypes">要记录的事件类型</param>
        public void SetRecordEventTypeFilter(params Type[] eventTypes)
        {
            lock (_lockObject)
            {
                _recordedEventTypes.Clear();
                if (eventTypes != null && eventTypes.Length > 0)
                {
                    foreach (var eventType in eventTypes)
                    {
                        _recordedEventTypes.Add(eventType);
                    }
                    _useEventTypeFilter = true;
                }
                else
                {
                    _useEventTypeFilter = false;
                }
            }
        }
        
        /// <summary>
        /// 设置记录的事件源过滤器
        /// </summary>
        /// <param name="sources">要记录的事件源</param>
        public void SetRecordSourceFilter(params string[] sources)
        {
            lock (_lockObject)
            {
                _recordedSources.Clear();
                if (sources != null && sources.Length > 0)
                {
                    foreach (var source in sources)
                    {
                        if (!string.IsNullOrEmpty(source))
                        {
                            _recordedSources.Add(source);
                        }
                    }
                    _useSourceFilter = true;
                }
                else
                {
                    _useSourceFilter = false;
                }
            }
        }
        
        /// <summary>
        /// 清除所有记录
        /// </summary>
        public void ClearRecords()
        {
            lock (_lockObject)
            {
                int count = _recordedEvents.Count;
                _recordedEvents.Clear();
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventReplaySystem: 清除了 {count} 个事件记录");
                }
            }
        }
        
        /// <summary>
        /// 生成重放统计报告
        /// </summary>
        /// <returns>统计报告字符串</returns>
        public string GenerateReplayStatistics()
        {
            lock (_lockObject)
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 事件重放统计报告 ===");
                report.AppendLine($"记录事件总数: {_recordedEvents.Count}");
                report.AppendLine($"当前状态: {(_isRecording ? "记录中" : _isReplaying ? "重放中" : "空闲")}");
                
                if (_recordedEvents.Count > 0)
                {
                    var firstEvent = _recordedEvents.First();
                    var lastEvent = _recordedEvents.Last();
                    var duration = lastEvent.Timestamp - firstEvent.Timestamp;
                    
                    report.AppendLine($"记录时间范围: {firstEvent.Timestamp:yyyy-MM-dd HH:mm:ss} - {lastEvent.Timestamp:yyyy-MM-dd HH:mm:ss}");
                    report.AppendLine($"记录持续时间: {duration.TotalSeconds:F2} 秒");
                    
                    // 事件类型统计
                    var eventTypeStats = _recordedEvents
                        .GroupBy(record => record.EventType)
                        .Select(group => new { EventType = group.Key, Count = group.Count() })
                        .OrderByDescending(stat => stat.Count)
                        .Take(10);
                    
                    report.AppendLine("\n=== 事件类型统计 (前10) ===");
                    foreach (var stat in eventTypeStats)
                    {
                        report.AppendLine($"{stat.EventType.Name}: {stat.Count} 次");
                    }
                }
                
                return report.ToString();
            }
        }
        
        #region 私有方法
        
        private void StartReplayCoroutine(int endIndex, float timeScale)
        {
            // 这里应该使用Unity的协程或者其他异步机制
            // 为了简化，这里使用同步方式
            ReplayEventsSync(endIndex, timeScale);
        }
        
        private void ReplayEventsSync(int endIndex, float timeScale)
        {
            var startTime = DateTime.UtcNow;
            
            for (int i = _currentReplayIndex; i <= endIndex && _isReplaying; i++)
            {
                var record = _recordedEvents[i];
                
                // 计算延迟时间
                if (i > _currentReplayIndex)
                {
                    var previousRecord = _recordedEvents[i - 1];
                    var delay = (record.RelativeTime - previousRecord.RelativeTime).TotalSeconds / timeScale;
                    
                    // 这里应该使用适当的延迟机制
                    System.Threading.Thread.Sleep((int)(delay * 1000));
                }
                
                ReplayEventRecord(record);
                _currentReplayIndex = i;
            }
            
            _isReplaying = false;
        }
        
        private void ReplayEventRecord(EventReplayRecord record)
        {
            try
            {
                if (record.EventData != null)
                {
                    // 重置事件状态
                    record.EventData.IsHandled = false;
                    if (record.EventData is EventBase eventBase)
                    {
                        eventBase.Reset();
                    }
                    
                    // 发布事件
                    var publishMethod = _eventBus.GetType().GetMethod("Publish");
                    var genericMethod = publishMethod.MakeGenericMethod(record.EventType);
                    genericMethod.Invoke(_eventBus, new object[] { record.EventData });
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"EventReplaySystem: 重放事件时发生错误 {record.EventTypeName}: {ex.Message}");
            }
        }
        
        private IEvent CloneEvent(IEvent originalEvent)
        {
            // 简化的克隆实现，实际应该使用深拷贝
            // 这里假设事件对象支持克隆或者使用序列化方式
            try
            {
                var eventType = originalEvent.GetType();
                var clonedEvent = Activator.CreateInstance(eventType) as IEvent;
                
                // 复制基本属性
                if (clonedEvent is EventBase clonedBase && originalEvent is EventBase originalBase)
                {
                    // 这里应该复制所有相关属性
                    // 为了简化，只复制基本信息
                }
                
                return clonedEvent ?? originalEvent;
            }
            catch
            {
                return originalEvent; // 如果克隆失败，返回原始事件
            }
        }
        
        #endregion
        
        public void Dispose()
        {
            StopRecording();
            StopReplay();
            ClearRecords();
        }
    }
    
    /// <summary>
    /// 事件重放记录
    /// </summary>
    public class EventReplayRecord
    {
        public string EventId { get; set; }
        public Type EventType { get; set; }
        public string EventTypeName { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; }
        public TimeSpan RelativeTime { get; set; }
        public int SubscriberCount { get; set; }
        public IEvent EventData { get; set; }
    }
}
