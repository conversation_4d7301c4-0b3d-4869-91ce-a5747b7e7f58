using NUnit.Framework;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// EventBus基础功能测试
    /// </summary>
    public class EventBusTests
    {
        private EventBus _eventBus;

        [SetUp]
        public void SetUp()
        {
            _eventBus = new EventBus(useObjectPool: true, debugMode: true);
        }

        [TearDown]
        public void TearDown()
        {
            _eventBus?.Dispose();
            _eventBus = null;
        }

        [Test]
        public void Subscribe_And_Publish_BasicEvent_ShouldWork()
        {
            // Arrange
            bool eventReceived = false;
            string receivedMenuItem = null;

            // Act
            _eventBus.Subscribe<MenuItemClickedEvent>(evt =>
            {
                eventReceived = true;
                receivedMenuItem = evt.MenuItem;
            });

            var testEvent = new MenuItemClickedEvent("File.New");
            _eventBus.Publish(testEvent);

            // Assert
            Assert.IsTrue(eventReceived, "事件应该被接收");
            Assert.AreEqual("File.New", receivedMenuItem, "菜单项名称应该匹配");
        }

        [Test]
        public void Subscribe_WithFilter_ShouldOnlyReceiveFilteredEvents()
        {
            // Arrange
            int eventCount = 0;

            // Act
            _eventBus.Subscribe<MenuItemClickedEvent>(
                evt => eventCount++,
                evt => evt.MenuItem.StartsWith("File.")
            );

            _eventBus.Publish(new MenuItemClickedEvent("File.New"));
            _eventBus.Publish(new MenuItemClickedEvent("Edit.Copy"));
            _eventBus.Publish(new MenuItemClickedEvent("File.Save"));

            // Assert
            Assert.AreEqual(2, eventCount, "应该只接收到2个File开头的事件");
        }

        [Test]
        public void Subscribe_WithPriority_ShouldExecuteInPriorityOrder()
        {
            // Arrange
            var executionOrder = new System.Collections.Generic.List<int>();

            // Act - 注意：优先级高的先执行
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => executionOrder.Add(1), 1);
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => executionOrder.Add(3), 3);
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => executionOrder.Add(2), 2);

            _eventBus.Publish(new MenuItemClickedEvent("Test"));

            // Assert
            Assert.AreEqual(3, executionOrder.Count, "应该执行3个处理器");
            Assert.AreEqual(3, executionOrder[0], "优先级3应该最先执行");
            Assert.AreEqual(2, executionOrder[1], "优先级2应该第二执行");
            Assert.AreEqual(1, executionOrder[2], "优先级1应该最后执行");
        }

        [Test]
        public void Unsubscribe_ShouldStopReceivingEvents()
        {
            // Arrange
            int eventCount = 0;

            void Handler(MenuItemClickedEvent evt)
            {
                eventCount++;
            }

            // Act
            _eventBus.Subscribe<MenuItemClickedEvent>(Handler);
            _eventBus.Publish(new MenuItemClickedEvent("Test1"));

            _eventBus.Unsubscribe<MenuItemClickedEvent>(Handler);
            _eventBus.Publish(new MenuItemClickedEvent("Test2"));

            // Assert
            Assert.AreEqual(1, eventCount, "取消订阅后应该不再接收事件");
        }

        [Test]
        public void Unsubscribe_WithSubscription_ShouldStopReceivingEvents()
        {
            // Arrange
            int eventCount = 0;

            // Act
            var subscription = _eventBus.Subscribe<MenuItemClickedEvent>(evt => eventCount++);
            _eventBus.Publish(new MenuItemClickedEvent("Test1"));

            _eventBus.Unsubscribe(subscription);
            _eventBus.Publish(new MenuItemClickedEvent("Test2"));

            // Assert
            Assert.AreEqual(1, eventCount, "通过订阅句柄取消订阅后应该不再接收事件");
            Assert.IsFalse(subscription.IsValid, "订阅句柄应该无效");
        }

        [Test]
        public void GetSubscriberCount_ShouldReturnCorrectCount()
        {
            // Arrange & Act
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "初始订阅者数量应该为0");

            var sub1 = _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            Assert.AreEqual(1, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "添加1个订阅者后应该为1");

            var sub2 = _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            Assert.AreEqual(2, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "添加2个订阅者后应该为2");

            _eventBus.Unsubscribe(sub1);
            Assert.AreEqual(1, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "取消1个订阅者后应该为1");
        }

        [Test]
        public void HasSubscribers_ShouldReturnCorrectValue()
        {
            // Arrange & Act
            Assert.IsFalse(_eventBus.HasSubscribers<MenuItemClickedEvent>(), "初始应该没有订阅者");

            var subscription = _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            Assert.IsTrue(_eventBus.HasSubscribers<MenuItemClickedEvent>(), "添加订阅者后应该有订阅者");

            _eventBus.Unsubscribe(subscription);
            Assert.IsFalse(_eventBus.HasSubscribers<MenuItemClickedEvent>(), "取消订阅者后应该没有订阅者");
        }

        [Test]
        public void ClearSubscriptions_ShouldRemoveAllSubscribers()
        {
            // Arrange
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            _eventBus.Subscribe<ToolSelectedEvent>(evt => { });

            // Act
            _eventBus.ClearSubscriptions<MenuItemClickedEvent>();

            // Assert
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "MenuItemClickedEvent订阅者应该被清除");
            Assert.AreEqual(1, _eventBus.GetSubscriberCount<ToolSelectedEvent>(), "ToolSelectedEvent订阅者应该保留");
        }

        [Test]
        public void ClearAllSubscriptions_ShouldRemoveAllSubscribers()
        {
            // Arrange
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            _eventBus.Subscribe<ToolSelectedEvent>(evt => { });

            // Act
            _eventBus.ClearAllSubscriptions();

            // Assert
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<MenuItemClickedEvent>(), "所有MenuItemClickedEvent订阅者应该被清除");
            Assert.AreEqual(0, _eventBus.GetSubscriberCount<ToolSelectedEvent>(), "所有ToolSelectedEvent订阅者应该被清除");
        }

        [Test]
        public void GetStatistics_ShouldReturnValidStatistics()
        {
            // Arrange
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            _eventBus.Subscribe<ToolSelectedEvent>(evt => { });

            // Act
            _eventBus.Publish(new MenuItemClickedEvent("Test"));
            _eventBus.Publish(new ToolSelectedEvent("TestTool"));

            var statistics = _eventBus.GetStatistics();

            // Assert
            Assert.IsNotNull(statistics, "统计信息不应该为空");
            Assert.AreEqual(2, statistics.TotalPublishedEvents, "应该发布了2个事件");
            Assert.AreEqual(2, statistics.ActiveEventTypes, "应该有2个活跃的事件类型");
        }

        [Test]
        public void PublishDelayed_ShouldPublishAfterDelay()
        {
            // Arrange
            bool eventReceived = false;
            _eventBus.Subscribe<MenuItemClickedEvent>(evt => eventReceived = true);

            // Act
            _eventBus.PublishDelayed(new MenuItemClickedEvent("DelayedTest"), 0.1f);

            // 立即检查 - 事件还没有发布
            Assert.IsFalse(eventReceived, "延迟事件不应该立即发布");

            // 模拟时间流逝并更新调度器
            System.Threading.Thread.Sleep(50); // 等待50ms
            _eventBus.Update(); // 手动调用Update来处理调度事件
            Assert.IsFalse(eventReceived, "0.05秒后事件还不应该发布");

            System.Threading.Thread.Sleep(100); // 再等待100ms
            _eventBus.Update(); // 手动调用Update来处理调度事件

            // Assert
            Assert.IsTrue(eventReceived, "延迟事件应该在延迟后发布");
        }
    }
}
