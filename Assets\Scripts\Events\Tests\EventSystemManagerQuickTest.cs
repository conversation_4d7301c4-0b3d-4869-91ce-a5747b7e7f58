using UnityEngine;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// EventSystemManager快速测试
    /// 用于验证修复后的EventSystemManager是否正常工作
    /// </summary>
    public class EventSystemManagerQuickTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runOnStart = true;
        [SerializeField] private float testDelay = 1f;

        private void Start()
        {
            if (runOnStart)
            {
                // 延迟测试，确保EventSystemManager已完全初始化
                Invoke(nameof(RunQuickTest), testDelay);
            }
        }

        [ContextMenu("运行快速测试")]
        public void RunQuickTest()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "=== 开始EventSystemManager快速测试 ===");

            // 测试1: 检查EventSystemManager实例
            TestEventSystemManagerInstance();

            // 测试2: 检查EventBus
            TestEventBus();

            // 测试3: 测试基本事件发布
            TestBasicEventPublishing();

            // 测试4: 测试兼容性API
            TestCompatibilityAPI();

            Logging.LogInfo("EventSystemManagerQuickTest", "=== EventSystemManager快速测试完成 ===");
        }

        private void TestEventSystemManagerInstance()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "测试1: 检查EventSystemManager实例");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                Logging.LogInfo("EventSystemManagerQuickTest", "✅ EventSystemManager实例存在");
                Logging.LogInfo("EventSystemManagerQuickTest", $"   GameObject: {manager.gameObject.name}");
                Logging.LogInfo("EventSystemManagerQuickTest", $"   组件状态: {(manager.enabled ? "启用" : "禁用")}");
            }
            else
            {
                Logging.LogError("EventSystemManagerQuickTest", "❌ EventSystemManager实例不存在");
            }
        }

        private void TestEventBus()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "测试2: 检查EventBus");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                var eventBus = manager.EventBus;
                if (eventBus != null)
                {
                    Logging.LogInfo("EventSystemManagerQuickTest", "✅ EventBus可用");
                    Logging.LogInfo("EventSystemManagerQuickTest", $"   EventBus类型: {eventBus.GetType().Name}");
                }
                else
                {
                    Logging.LogError("EventSystemManagerQuickTest", "❌ EventBus不可用");
                }
            }
            else
            {
                Logging.LogError("EventSystemManagerQuickTest", "❌ 无法测试EventBus，EventSystemManager不存在");
            }
        }

        private void TestBasicEventPublishing()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "测试3: 测试基本事件发布");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager?.EventBus != null)
            {
                try
                {
                    bool eventReceived = false;
                    string receivedMessage = "";

                    // 订阅测试事件
                    var subscription = manager.EventBus.Subscribe<StatusMessageEvent>(evt =>
                    {
                        eventReceived = true;
                        receivedMessage = evt.Message;
                        Logging.LogInfo("EventSystemManagerQuickTest", $"   收到事件: {evt.Message}");
                    });

                    // 发布测试事件
                    var testMessage = "EventSystemManager快速测试消息";
                    manager.EventBus.Publish(new StatusMessageEvent(testMessage));

                    // 检查结果（由于是同步的，应该立即收到）
                    if (eventReceived && receivedMessage == testMessage)
                    {
                        Logging.LogInfo("EventSystemManagerQuickTest", "✅ 基本事件发布和接收正常");
                    }
                    else
                    {
                        Logging.LogError("EventSystemManagerQuickTest", $"❌ 事件发布测试失败 - 接收: {eventReceived}, 消息: '{receivedMessage}'");
                    }

                    // 清理订阅
                    subscription?.Dispose();
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("EventSystemManagerQuickTest", $"❌ 事件发布测试异常: {ex.Message}");
                }
            }
            else
            {
                Logging.LogError("EventSystemManagerQuickTest", "❌ 无法测试事件发布，EventBus不可用");
            }
        }

        private void TestCompatibilityAPI()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "测试4: 测试兼容性API");

            try
            {
                // 测试兼容性API是否可用
                if (BlastingDesign.Events.Compatibility.UIEventSystemCompat.IsInitialized)
                {
                    Logging.LogInfo("EventSystemManagerQuickTest", "✅ 兼容性API已初始化");

                    // 测试兼容性事件发布
                    BlastingDesign.Events.Compatibility.UIEventSystemCompat.TriggerStatusMessage("兼容性API测试消息");
                    Logging.LogInfo("EventSystemManagerQuickTest", "✅ 兼容性事件发布成功");
                }
                else
                {
                    Logging.LogWarning("EventSystemManagerQuickTest", "⚠️ 兼容性API未初始化（可能是正常的，如果禁用了兼容性模式）");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("EventSystemManagerQuickTest", $"❌ 兼容性API测试异常: {ex.Message}");
            }
        }

        [ContextMenu("测试事件系统配置")]
        public void TestEventSystemConfiguration()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "=== 测试事件系统配置 ===");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                // 通过反射获取配置信息（因为字段是private）
                var managerType = manager.GetType();
                
                try
                {
                    var enableLegacyField = managerType.GetField("enableLegacyCompatibility", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var enableInputField = managerType.GetField("enableInputIntegration", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var showDebugField = managerType.GetField("showDebugInfo", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (enableLegacyField != null)
                    {
                        bool enableLegacy = (bool)enableLegacyField.GetValue(manager);
                        Logging.LogInfo("EventSystemManagerQuickTest", $"兼容性模式: {(enableLegacy ? "启用" : "禁用")}");
                    }

                    if (enableInputField != null)
                    {
                        bool enableInput = (bool)enableInputField.GetValue(manager);
                        Logging.LogInfo("EventSystemManagerQuickTest", $"输入集成: {(enableInput ? "启用" : "禁用")}");
                    }

                    if (showDebugField != null)
                    {
                        bool showDebug = (bool)showDebugField.GetValue(manager);
                        Logging.LogInfo("EventSystemManagerQuickTest", $"调试信息: {(showDebug ? "启用" : "禁用")}");
                    }
                }
                catch (System.Exception ex)
                {
                    Logging.LogWarning("EventSystemManagerQuickTest", $"无法获取配置信息: {ex.Message}");
                }

                // 测试可用的服务
                Logging.LogInfo("EventSystemManagerQuickTest", $"EventBus: {(manager.EventBus != null ? "可用" : "不可用")}");
                Logging.LogInfo("EventSystemManagerQuickTest", $"EventTypeRegistry: {(manager.EventTypeRegistry != null ? "可用" : "不可用")}");
                Logging.LogInfo("EventSystemManagerQuickTest", $"FilterManager: {(manager.FilterManager != null ? "可用" : "不可用")}");
            }
            else
            {
                Logging.LogError("EventSystemManagerQuickTest", "❌ EventSystemManager实例不存在");
            }

            Logging.LogInfo("EventSystemManagerQuickTest", "=== 配置测试完成 ===");
        }

        [ContextMenu("强制重新初始化")]
        public void ForceReinitialize()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "强制重新初始化EventSystemManager");

            var manager = BlastingDesign.Events.EventSystemManager.Instance;
            if (manager != null)
            {
                // 销毁当前实例
                DestroyImmediate(manager.gameObject);
                
                // 等待一帧后重新创建
                Invoke(nameof(CreateNewEventSystemManager), 0.1f);
            }
            else
            {
                CreateNewEventSystemManager();
            }
        }

        private void CreateNewEventSystemManager()
        {
            Logging.LogInfo("EventSystemManagerQuickTest", "创建新的EventSystemManager实例");

            var go = new GameObject("EventSystemManager");
            go.AddComponent<BlastingDesign.Events.EventSystemManager>();
            
            Logging.LogInfo("EventSystemManagerQuickTest", "新的EventSystemManager已创建");
        }
    }
}
