using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件对象池
    /// 用于减少GC压力和内存分配
    /// </summary>
    public class EventPool : IDisposable
    {
        private readonly ConcurrentDictionary<Type, Queue<IEvent>> _pools;
        private readonly object _lockObject = new object();
        private readonly int _maxPoolSize;
        private readonly bool _enableDebugLogging;
        
        // 统计信息
        private long _totalGets = 0;
        private long _totalReturns = 0;
        private long _totalCreated = 0;
        private long _totalPoolHits = 0;
        
        public EventPool(int maxPoolSize = 100, bool enableDebugLogging = false)
        {
            _pools = new ConcurrentDictionary<Type, Queue<IEvent>>();
            _maxPoolSize = maxPoolSize;
            _enableDebugLogging = enableDebugLogging;
        }
        
        /// <summary>
        /// 从池中获取事件对象
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>事件对象</returns>
        public T Get<T>() where T : IEvent, new()
        {
            return (T)Get(typeof(T));
        }
        
        /// <summary>
        /// 从池中获取事件对象
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件对象</returns>
        public IEvent Get(Type eventType)
        {
            _totalGets++;
            
            if (!_pools.TryGetValue(eventType, out Queue<IEvent> pool))
            {
                pool = new Queue<IEvent>();
                _pools[eventType] = pool;
            }
            
            IEvent eventObj = null;
            
            lock (_lockObject)
            {
                if (pool.Count > 0)
                {
                    eventObj = pool.Dequeue();
                    _totalPoolHits++;
                    
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"EventPool: 从池中获取事件 {eventType.Name}，池中剩余: {pool.Count}");
                    }
                }
            }
            
            if (eventObj == null)
            {
                eventObj = (IEvent)Activator.CreateInstance(eventType);
                _totalCreated++;
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventPool: 创建新事件对象 {eventType.Name}");
                }
            }
            
            // 重置事件状态
            if (eventObj is EventBase eventBase)
            {
                eventBase.Reset();
            }
            
            return eventObj;
        }
        
        /// <summary>
        /// 将事件对象返回到池中
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件对象</param>
        public void Return<T>(T eventData) where T : IEvent
        {
            Return(eventData);
        }
        
        /// <summary>
        /// 将事件对象返回到池中
        /// </summary>
        /// <param name="eventData">事件对象</param>
        public void Return(IEvent eventData)
        {
            if (eventData == null) return;
            
            _totalReturns++;
            
            Type eventType = eventData.GetType();
            
            if (!_pools.TryGetValue(eventType, out Queue<IEvent> pool))
            {
                pool = new Queue<IEvent>();
                _pools[eventType] = pool;
            }
            
            lock (_lockObject)
            {
                if (pool.Count < _maxPoolSize)
                {
                    // 清理事件状态
                    eventData.IsHandled = false;
                    if (eventData is EventBase eventBase)
                    {
                        eventBase.Reset();
                    }
                    
                    pool.Enqueue(eventData);
                    
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"EventPool: 返回事件到池 {eventType.Name}，池中数量: {pool.Count}");
                    }
                }
                else if (_enableDebugLogging)
                {
                    Debug.Log($"EventPool: 池已满，丢弃事件 {eventType.Name}");
                }
            }
        }
        
        /// <summary>
        /// 清空指定类型的池
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void Clear<T>() where T : IEvent
        {
            Clear(typeof(T));
        }
        
        /// <summary>
        /// 清空指定类型的池
        /// </summary>
        /// <param name="eventType">事件类型</param>
        public void Clear(Type eventType)
        {
            if (_pools.TryGetValue(eventType, out Queue<IEvent> pool))
            {
                lock (_lockObject)
                {
                    int count = pool.Count;
                    pool.Clear();
                    
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"EventPool: 清空池 {eventType.Name}，清理了 {count} 个对象");
                    }
                }
            }
        }
        
        /// <summary>
        /// 清空所有池
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                int totalCleared = 0;
                foreach (var pool in _pools.Values)
                {
                    totalCleared += pool.Count;
                    pool.Clear();
                }
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventPool: 清空所有池，总共清理了 {totalCleared} 个对象");
                }
            }
        }
        
        /// <summary>
        /// 获取池统计信息
        /// </summary>
        /// <returns>池统计信息</returns>
        public EventPoolStatistics GetStatistics()
        {
            var typeStatistics = new Dictionary<Type, EventPoolTypeStatistics>();
            
            lock (_lockObject)
            {
                foreach (var kvp in _pools)
                {
                    typeStatistics[kvp.Key] = new EventPoolTypeStatistics
                    {
                        EventType = kvp.Key,
                        PooledCount = kvp.Value.Count
                    };
                }
            }
            
            return new EventPoolStatistics
            {
                TotalGets = _totalGets,
                TotalReturns = _totalReturns,
                TotalCreated = _totalCreated,
                TotalPoolHits = _totalPoolHits,
                PoolHitRate = _totalGets > 0 ? (double)_totalPoolHits / _totalGets : 0,
                TypeStatistics = typeStatistics
            };
        }
        
        public void Dispose()
        {
            ClearAll();
            _pools.Clear();
        }
    }
    
    /// <summary>
    /// 事件池统计信息
    /// </summary>
    public class EventPoolStatistics
    {
        public long TotalGets { get; set; }
        public long TotalReturns { get; set; }
        public long TotalCreated { get; set; }
        public long TotalPoolHits { get; set; }
        public double PoolHitRate { get; set; }
        public Dictionary<Type, EventPoolTypeStatistics> TypeStatistics { get; set; }
    }
    
    /// <summary>
    /// 事件池类型统计信息
    /// </summary>
    public class EventPoolTypeStatistics
    {
        public Type EventType { get; set; }
        public int PooledCount { get; set; }
    }
}
