# 工具栏堆栈溢出问题修复报告

## 问题概述

**问题描述**: 在工具栏上调用PublishEvent方法时，会产生堆栈溢出错误，导致程序崩溃。

**错误信息**: `The requested operation caused a stack overflow.`

**影响范围**: 工具栏组件的事件发布功能，可能影响整个UI事件系统的稳定性。

## 问题分析

### 根本原因

通过代码分析，发现堆栈溢出的主要原因是：

1. **缺乏重入保护**: `HandleToolSelection`和`HandleToggleButton`方法没有重入保护机制
2. **事件处理器中的递归调用**: 某些事件处理器可能在响应`ToolGroupSwitchedEvent`或`ToolStateChangedEvent`时触发新的工具选择操作
3. **同步事件发布**: 在同一个调用栈中连续发布多个相关事件，增加了递归调用的风险

### 问题调用链

```
用户点击工具按钮
↓
OnToolButtonClicked
↓
HandleToolSelection
↓
PublishEvent(ToolGroupSwitchedEvent)
↓
事件处理器响应
↓
可能触发新的工具选择操作
↓
重新进入HandleToolSelection
↓
堆栈溢出
```

## 解决方案

### 1. 添加重入保护机制

在`Toolbar`类中添加了重入保护标志：

```csharp
// 重入保护
private bool isProcessingToolSelection = false;
```

### 2. 修改HandleToolSelection方法

```csharp
private void HandleToolSelection(ToolbarConfig.ToolButton buttonConfig, Button button)
{
    // 重入保护 - 防止堆栈溢出
    if (isProcessingToolSelection)
    {
        Logging.LogWarning("Toolbar", $"检测到工具选择重入调用，忽略: {buttonConfig.name}");
        return;
    }

    try
    {
        isProcessingToolSelection = true;
        // ... 原有逻辑
        
        // 延迟发布事件，避免在状态变更过程中触发递归调用
        PublishToolSelectionEvents(buttonConfig, previousTool);
    }
    finally
    {
        isProcessingToolSelection = false;
    }
}
```

### 3. 分离事件发布逻辑

创建了专门的事件发布方法：

```csharp
private void PublishToolSelectionEvents(ToolbarConfig.ToolButton buttonConfig, string previousTool)
{
    try
    {
        // 发布工具组切换事件
        // 发布工具状态变更事件
    }
    catch (System.Exception ex)
    {
        Logging.LogError("Toolbar", $"发布工具选择事件时出错: {ex.Message}");
    }
}
```

### 4. 增强错误处理

在`UIElementBase.PublishEvent`方法中添加了专门的堆栈溢出检测：

```csharp
catch (StackOverflowException)
{
    Logging.LogError(elementName, $"发布事件时发生堆栈溢出: {typeof(T).Name}，可能存在循环调用");
    throw; // 重新抛出堆栈溢出异常，因为这是严重错误
}
```

## 修改的文件

### 1. Assets/Scripts/UI/Components/Toolbar.cs
- 添加重入保护标志 `isProcessingToolSelection`
- 修改 `HandleToolSelection` 方法，添加重入保护
- 修改 `HandleToggleButton` 方法，添加重入保护
- 新增 `PublishToolSelectionEvents` 方法，分离事件发布逻辑

### 2. Assets/Scripts/UI/Core/UIElementBase.cs
- 增强 `PublishEvent` 方法的错误处理
- 添加堆栈溢出异常的专门处理
- 改进错误日志记录

### 3. Assets/Scripts/Events/Tests/ToolbarStackOverflowTest.cs (新增)
- 创建专门的测试脚本验证修复效果
- 模拟递归调用场景
- 验证重入保护机制是否有效
- 修复了事件订阅清理的方法调用问题

### 4. Assets/Scripts/Events/Tests/EventSubscriptionTest.cs (新增)
- 创建事件订阅测试脚本
- 验证正确的订阅和取消订阅方法
- 提供事件系统功能验证工具

## 测试验证

### 测试方法

1. **使用测试脚本**: 运行 `ToolbarStackOverflowTest` 脚本
2. **手动测试**: 快速连续点击工具栏按钮
3. **压力测试**: 在事件处理器中模拟递归调用

### 预期结果

- 不再出现堆栈溢出错误
- 重入调用被正确阻止并记录警告日志
- 工具栏功能正常工作

## 使用建议

### 1. 监控日志

注意观察以下警告日志：
- `"检测到工具选择重入调用，忽略"`
- `"检测到切换按钮重入调用，忽略"`

### 2. 事件处理器最佳实践

在编写事件处理器时：
- 避免在事件处理器中直接调用工具栏方法
- 如需触发工具选择，使用延迟调用或异步方式
- 检查事件来源，避免处理自己发布的事件

### 3. 调试工具

使用 `ToolbarStackOverflowTest` 脚本进行定期测试：
```csharp
// 在Inspector中启用测试
enableTest = true;
enableDetailedLogging = true;

// 或通过右键菜单
[ContextMenu("开始堆栈溢出测试")]
```

## 附加修复：事件订阅清理问题

在修复过程中，还发现并解决了一个事件订阅清理的问题：

### 问题描述
测试脚本中使用了不存在的`subscription.Unsubscribe()`方法，导致编译错误。

### 解决方案
正确的事件订阅清理方法：

```csharp
// 方法1：使用EventBus的Unsubscribe方法（推荐）
eventBus.Unsubscribe(subscription);

// 方法2：直接Dispose订阅
subscription.Dispose();

// 完整的清理代码示例
private void CleanupSubscriptions()
{
    var eventBus = EventSystemManager.Instance?.EventBus;

    foreach (var subscription in subscriptions)
    {
        if (subscription != null)
        {
            if (eventBus != null)
            {
                eventBus.Unsubscribe(subscription);
            }
            else
            {
                subscription.Dispose();
            }
        }
    }
    subscriptions.Clear();
}
```

## 后续改进建议

1. **异步事件发布**: 考虑实现异步事件发布机制
2. **事件优先级**: 实现事件优先级系统，避免重要事件被阻止
3. **性能监控**: 添加事件发布性能监控
4. **自动化测试**: 集成到CI/CD流程中进行自动化测试
5. **订阅管理**: 考虑实现自动订阅管理器，简化订阅生命周期管理

## 总结

通过添加重入保护机制和改进错误处理，成功解决了工具栏中PublishEvent方法导致的堆栈溢出问题。修复方案保持了原有功能的完整性，同时提高了系统的稳定性和可靠性。
