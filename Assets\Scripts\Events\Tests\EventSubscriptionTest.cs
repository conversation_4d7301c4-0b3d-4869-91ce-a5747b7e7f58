using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;
using System.Collections.Generic;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 事件订阅测试脚本
    /// 验证事件订阅和取消订阅的正确性
    /// </summary>
    public class EventSubscriptionTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runTestOnStart = false;
        [SerializeField] private bool enableDetailedLogging = true;

        private List<IEventSubscription> testSubscriptions = new List<IEventSubscription>();
        private int eventCount = 0;

        private void Start()
        {
            if (runTestOnStart)
            {
                StartSubscriptionTest();
            }
        }

        [ContextMenu("开始订阅测试")]
        public void StartSubscriptionTest()
        {
            Logging.LogInfo("EventSubscriptionTest", "开始事件订阅测试");

            // 清理之前的订阅
            CleanupSubscriptions();

            // 测试事件订阅
            TestEventSubscription();

            // 测试事件发布
            TestEventPublishing();

            // 测试订阅清理
            TestSubscriptionCleanup();

            Logging.LogInfo("EventSubscriptionTest", "事件订阅测试完成");
        }

        private void TestEventSubscription()
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager == null)
            {
                Logging.LogError("EventSubscriptionTest", "EventSystemManager未找到");
                return;
            }

            var eventBus = eventSystemManager.EventBus;
            if (eventBus == null)
            {
                Logging.LogError("EventSubscriptionTest", "EventBus未找到");
                return;
            }

            try
            {
                // 订阅工具状态变更事件
                var subscription1 = eventBus.Subscribe<ToolStateChangedEvent>(OnTestToolStateChanged);
                if (subscription1 != null)
                {
                    testSubscriptions.Add(subscription1);
                    Logging.LogInfo("EventSubscriptionTest", "成功订阅ToolStateChangedEvent");
                }

                // 订阅工具选择事件
                var subscription2 = eventBus.Subscribe<ToolSelectedEvent>(OnTestToolSelected);
                if (subscription2 != null)
                {
                    testSubscriptions.Add(subscription2);
                    Logging.LogInfo("EventSubscriptionTest", "成功订阅ToolSelectedEvent");
                }

                // 订阅状态消息事件
                var subscription3 = eventBus.Subscribe<StatusMessageEvent>(OnTestStatusMessage);
                if (subscription3 != null)
                {
                    testSubscriptions.Add(subscription3);
                    Logging.LogInfo("EventSubscriptionTest", "成功订阅StatusMessageEvent");
                }

                Logging.LogInfo("EventSubscriptionTest", $"总共订阅了 {testSubscriptions.Count} 个事件");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("EventSubscriptionTest", $"订阅事件时出错: {ex.Message}");
            }
        }

        private void TestEventPublishing()
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            var eventBus = eventSystemManager?.EventBus;

            if (eventBus == null)
            {
                Logging.LogError("EventSubscriptionTest", "EventBus不可用，无法测试事件发布");
                return;
            }

            try
            {
                // 发布测试事件
                eventBus.Publish(new ToolStateChangedEvent(
                    "test-tool",
                    ToolState.Active,
                    ToolState.Inactive,
                    null,
                    "EventSubscriptionTest"
                ));

                eventBus.Publish(new ToolSelectedEvent("test-tool", "EventSubscriptionTest"));

                eventBus.Publish(new StatusMessageEvent("测试状态消息", "EventSubscriptionTest"));

                Logging.LogInfo("EventSubscriptionTest", "测试事件发布完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("EventSubscriptionTest", $"发布事件时出错: {ex.Message}");
            }
        }

        private void TestSubscriptionCleanup()
        {
            Logging.LogInfo("EventSubscriptionTest", "开始测试订阅清理");

            int initialCount = testSubscriptions.Count;
            CleanupSubscriptions();

            if (testSubscriptions.Count == 0)
            {
                Logging.LogInfo("EventSubscriptionTest", $"订阅清理成功，清理了 {initialCount} 个订阅");
            }
            else
            {
                Logging.LogError("EventSubscriptionTest", $"订阅清理失败，仍有 {testSubscriptions.Count} 个订阅未清理");
            }
        }

        private void OnTestToolStateChanged(ToolStateChangedEvent evt)
        {
            eventCount++;
            if (enableDetailedLogging)
            {
                Logging.LogInfo("EventSubscriptionTest", 
                    $"收到工具状态变更事件 #{eventCount}: {evt.ToolName} - {evt.PreviousState} -> {evt.CurrentState}");
            }
        }

        private void OnTestToolSelected(ToolSelectedEvent evt)
        {
            eventCount++;
            if (enableDetailedLogging)
            {
                Logging.LogInfo("EventSubscriptionTest", 
                    $"收到工具选择事件 #{eventCount}: {evt.ToolName}");
            }
        }

        private void OnTestStatusMessage(StatusMessageEvent evt)
        {
            eventCount++;
            if (enableDetailedLogging)
            {
                Logging.LogInfo("EventSubscriptionTest", 
                    $"收到状态消息事件 #{eventCount}: {evt.Message}");
            }
        }

        private void CleanupSubscriptions()
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            var eventBus = eventSystemManager != null ? eventSystemManager.EventBus : null;

            foreach (var subscription in testSubscriptions)
            {
                if (subscription != null)
                {
                    try
                    {
                        // 优先使用EventBus的Unsubscribe方法
                        if (eventBus != null)
                        {
                            eventBus.Unsubscribe(subscription);
                        }
                        else
                        {
                            // 如果EventBus不可用，直接Dispose
                            subscription.Dispose();
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Logging.LogError("EventSubscriptionTest", $"清理订阅时出错: {ex.Message}");
                    }
                }
            }

            testSubscriptions.Clear();
        }

        private void OnDestroy()
        {
            CleanupSubscriptions();
        }

        [ContextMenu("重置测试")]
        public void ResetTest()
        {
            CleanupSubscriptions();
            eventCount = 0;
            Logging.LogInfo("EventSubscriptionTest", "测试已重置");
        }

        [ContextMenu("显示统计信息")]
        public void ShowStatistics()
        {
            Logging.LogInfo("EventSubscriptionTest", 
                $"统计信息 - 当前订阅数: {testSubscriptions.Count}, 收到事件数: {eventCount}");
        }
    }
}
