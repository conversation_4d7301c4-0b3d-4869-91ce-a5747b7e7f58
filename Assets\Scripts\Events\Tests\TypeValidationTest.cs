using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Tests;
using BlastingDesign.Utils;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 类型验证测试
    /// 验证所有事件类型和组件类型是否正确定义
    /// </summary>
    public class TypeValidationTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runOnStart = true;

        private void Start()
        {
            if (runOnStart)
            {
                RunTypeValidationTest();
            }
        }

        [ContextMenu("运行类型验证测试")]
        public void RunTypeValidationTest()
        {
            Logging.LogInfo("TypeValidationTest", "开始类型验证测试");

            // 测试事件类型
            TestEventTypes();

            // 测试UI组件类型
            TestUIComponentTypes();

            // 测试兼容性类型
            TestCompatibilityTypes();

            // 生成测试报告
            TestReportGeneration();

            Logging.LogInfo("TypeValidationTest", "类型验证测试完成");
        }

        private void TestEventTypes()
        {
            Logging.LogInfo("TypeValidationTest", "测试事件类型");

            // 测试基础事件类型
            TestType<StatusMessageEvent>("StatusMessageEvent");
            TestType<ObjectSelectedEvent>("ObjectSelectedEvent");
            TestType<PropertyChangedEvent>("PropertyChangedEvent");
            TestType<CustomEvent>("CustomEvent");

            // 测试Toolbar事件类型
            TestType<ToolButtonClickedEvent>("ToolButtonClickedEvent");
            TestType<ToolSelectedEvent>("ToolSelectedEvent");
            TestType<ToolStateChangedEvent>("ToolStateChangedEvent");
            TestType<ToolGroupSwitchedEvent>("ToolGroupSwitchedEvent");

            // 测试其他事件类型
            TestType<MenuItemClickedEvent>("MenuItemClickedEvent");
            TestType<PanelEvent>("PanelEvent");
        }

        private void TestUIComponentTypes()
        {
            Logging.LogInfo("TypeValidationTest", "测试UI组件类型");

            // 这些类型不能用FindObjectOfType，但可以检查类型是否存在
            TestTypeExists("BlastingDesign.UI.Components.Toolbar");
            TestTypeExists("BlastingDesign.UI.Components.StatusBar");
            TestTypeExists("BlastingDesign.UI.Core.UIElementBase");
        }

        private void TestCompatibilityTypes()
        {
            Logging.LogInfo("TypeValidationTest", "测试兼容性类型");

            TestTypeExists("BlastingDesign.Events.Compatibility.UIEventSystemAdapter");
            TestTypeExists("BlastingDesign.Events.Compatibility.UIEventSystemCompat");
            TestTypeExists("BlastingDesign.UI.Core.UIEventSystem");
        }

        private void TestReportGeneration()
        {
            Logging.LogInfo("TypeValidationTest", "测试报告生成");

            try
            {
                var report = MigrationTestReport.GenerateToolbarMigrationReport();
                if (!string.IsNullOrEmpty(report))
                {
                    Logging.LogInfo("TypeValidationTest", "✅ 报告生成成功");
                    Logging.LogInfo("TypeValidationTest", $"报告长度: {report.Length} 字符");
                }
                else
                {
                    Logging.LogError("TypeValidationTest", "❌ 报告生成失败 - 报告为空");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("TypeValidationTest", $"❌ 报告生成异常: {ex.Message}");
            }
        }

        private void TestType<T>(string typeName) where T : class
        {
            try
            {
                var type = typeof(T);
                if (type != null)
                {
                    Logging.LogInfo("TypeValidationTest", $"✅ {typeName} 类型存在");
                    
                    // 如果是事件类型，测试创建实例
                    if (typeof(IEvent).IsAssignableFrom(type))
                    {
                        TestEventInstantiation<T>(typeName);
                    }
                }
                else
                {
                    Logging.LogError("TypeValidationTest", $"❌ {typeName} 类型不存在");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("TypeValidationTest", $"❌ {typeName} 类型测试异常: {ex.Message}");
            }
        }

        private void TestEventInstantiation<T>(string typeName) where T : class
        {
            try
            {
                // 尝试创建事件实例（使用反射，因为不同事件有不同的构造函数）
                var type = typeof(T);
                var constructors = type.GetConstructors();
                
                if (constructors.Length > 0)
                {
                    // 尝试使用第一个构造函数
                    var constructor = constructors[0];
                    var parameters = constructor.GetParameters();
                    var args = new object[parameters.Length];
                    
                    // 为参数提供默认值
                    for (int i = 0; i < parameters.Length; i++)
                    {
                        var paramType = parameters[i].ParameterType;
                        if (paramType == typeof(string))
                            args[i] = $"Test{typeName}";
                        else if (paramType == typeof(bool))
                            args[i] = false;
                        else if (paramType == typeof(int))
                            args[i] = 0;
                        else if (paramType == typeof(float))
                            args[i] = 0f;
                        else if (paramType == typeof(Vector3))
                            args[i] = Vector3.zero;
                        else if (paramType == typeof(Vector2))
                            args[i] = Vector2.zero;
                        else if (paramType.IsEnum)
                            args[i] = System.Enum.GetValues(paramType).GetValue(0);
                        else
                            args[i] = null;
                    }
                    
                    var instance = constructor.Invoke(args);
                    if (instance != null)
                    {
                        Logging.LogInfo("TypeValidationTest", $"✅ {typeName} 实例创建成功");
                    }
                    else
                    {
                        Logging.LogWarning("TypeValidationTest", $"⚠️ {typeName} 实例创建返回null");
                    }
                }
                else
                {
                    Logging.LogWarning("TypeValidationTest", $"⚠️ {typeName} 没有公共构造函数");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogWarning("TypeValidationTest", $"⚠️ {typeName} 实例创建失败: {ex.Message}");
            }
        }

        private void TestTypeExists(string typeName)
        {
            try
            {
                var type = System.Type.GetType(typeName);
                if (type == null)
                {
                    // 尝试在所有程序集中查找
                    var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
                    foreach (var assembly in assemblies)
                    {
                        type = assembly.GetType(typeName);
                        if (type != null) break;
                    }
                }

                if (type != null)
                {
                    Logging.LogInfo("TypeValidationTest", $"✅ {typeName} 类型存在");
                }
                else
                {
                    Logging.LogError("TypeValidationTest", $"❌ {typeName} 类型不存在");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("TypeValidationTest", $"❌ {typeName} 类型检查异常: {ex.Message}");
            }
        }

        [ContextMenu("保存测试报告")]
        public void SaveTestReport()
        {
            try
            {
                var report = MigrationTestReport.GenerateToolbarMigrationReport();
                MigrationTestReport.SaveReportToFile(report, "TypeValidationTestReport.md");
                Logging.LogInfo("TypeValidationTest", "测试报告已保存");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("TypeValidationTest", $"保存测试报告失败: {ex.Message}");
            }
        }
    }
}
