// 新事件系统在实际项目中的使用示例
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.EventSystem.Examples
{
    #region 实际使用示例
    
    /// <summary>
    /// 左侧面板使用新事件系统的示例
    /// </summary>
    public class NewLeftPanel : VisualElement
    {
        private readonly IEventBus _eventBus;
        private readonly IEventSubscriptionManager _subscriptionManager;
        private readonly TreeSearchEventHandler _searchHandler;
        
        private NewDaisyTree _blastingTree;
        private NewDaisyTree _layerTree;
        private NewDaisyTree _materialTree;
        
        public NewLeftPanel(IEventBus eventBus, TreeSearchEventHandler searchHandler)
        {
            _eventBus = eventBus;
            _searchHandler = searchHandler;
            _subscriptionManager = new EventSubscriptionManager(_eventBus);
            
            InitializePanel();
            SetupEventSubscriptions();
        }
        
        private void InitializePanel()
        {
            // 创建爆破设计树
            _blastingTree = new NewDaisyTree(
                _eventBus,
                "blasting-tree",
                new TreeSearchConfig
                {
                    MinQueryLength = 2,
                    AutoExpandMatches = true,
                    SearchInText = true,
                    SearchInIcon = true
                }
            );
            
            // 创建图层树
            _layerTree = new NewDaisyTree(
                _eventBus,
                "layer-tree",
                new TreeSearchConfig
                {
                    MinQueryLength = 1,
                    AutoExpandMatches = false,
                    SearchInText = true,
                    SearchInIcon = false
                }
            );
            
            // 创建材质树
            _materialTree = new NewDaisyTree(
                _eventBus,
                "material-tree",
                new TreeSearchConfig
                {
                    MinQueryLength = 3,
                    AutoExpandMatches = true,
                    SearchInText = true,
                    SearchInIcon = true,
                    EnablePerformanceMonitoring = true
                }
            );
            
            // 注册到搜索处理器
            RegisterTreeComponents();
            
            // 添加到面板
            Add(_blastingTree);
            Add(_layerTree);
            Add(_materialTree);
        }
        
        private void RegisterTreeComponents()
        {
            // 注册爆破树数据
            var blastingData = CreateBlastingTreeData();
            _searchHandler.RegisterComponent("blasting-tree", blastingData, _blastingTree.SearchConfig);
            _blastingTree.SetTreeData(blastingData);
            
            // 注册图层树数据
            var layerData = CreateLayerTreeData();
            _searchHandler.RegisterComponent("layer-tree", layerData, _layerTree.SearchConfig);
            _layerTree.SetTreeData(layerData);
            
            // 注册材质树数据
            var materialData = CreateMaterialTreeData();
            _searchHandler.RegisterComponent("material-tree", materialData, _materialTree.SearchConfig);
            _materialTree.SetTreeData(materialData);
        }
        
        private void SetupEventSubscriptions()
        {
            // 监听全局搜索事件
            _subscriptionManager.Subscribe<GlobalSearchEvent>(HandleGlobalSearch);
            
            // 监听树选择事件
            _subscriptionManager.Subscribe<TreeSelectionEvent>(HandleTreeSelection);
            
            // 监听数据更新事件
            _subscriptionManager.Subscribe<ProjectDataUpdateEvent>(HandleProjectDataUpdate);
        }
        
        private void HandleGlobalSearch(GlobalSearchEvent evt)
        {
            // 全局搜索时，在所有树中搜索
            _blastingTree.ProgrammaticSearch(evt.SearchQuery);
            _layerTree.ProgrammaticSearch(evt.SearchQuery);
            _materialTree.ProgrammaticSearch(evt.SearchQuery);
        }
        
        private void HandleTreeSelection(TreeSelectionEvent evt)
        {
            // 处理树选择事件
            Debug.Log($"Tree item selected: {evt.SelectedItem} in {evt.ComponentId}");
            
            // 发布选择事件到UI系统
            _eventBus.Publish(new UISelectionEvent(evt.ComponentId, evt.SelectedItem));
        }
        
        private void HandleProjectDataUpdate(ProjectDataUpdateEvent evt)
        {
            // 项目数据更新时，重新加载树数据
            switch (evt.DataType)
            {
                case ProjectDataType.BlastingDesign:
                    var newBlastingData = CreateBlastingTreeData();
                    _searchHandler.RegisterComponent("blasting-tree", newBlastingData, _blastingTree.SearchConfig);
                    _blastingTree.SetTreeData(newBlastingData);
                    break;
                    
                case ProjectDataType.Layers:
                    var newLayerData = CreateLayerTreeData();
                    _searchHandler.RegisterComponent("layer-tree", newLayerData, _layerTree.SearchConfig);
                    _layerTree.SetTreeData(newLayerData);
                    break;
                    
                case ProjectDataType.Materials:
                    var newMaterialData = CreateMaterialTreeData();
                    _searchHandler.RegisterComponent("material-tree", newMaterialData, _materialTree.SearchConfig);
                    _materialTree.SetTreeData(newMaterialData);
                    break;
            }
        }
        
        public void Dispose()
        {
            // 清理资源
            _subscriptionManager?.Dispose();
            _blastingTree?.Dispose();
            _layerTree?.Dispose();
            _materialTree?.Dispose();
            
            // 从搜索处理器中注销
            _searchHandler.UnregisterComponent("blasting-tree");
            _searchHandler.UnregisterComponent("layer-tree");
            _searchHandler.UnregisterComponent("material-tree");
        }
        
        private List<DaisyTreeData> CreateBlastingTreeData()
        {
            // 创建爆破设计数据（示例）
            return new List<DaisyTreeData>
            {
                new DaisyTreeData("blast-1", "爆破区域1", "blast-icon")
                {
                    Children = new List<DaisyTreeData>
                    {
                        new DaisyTreeData("hole-1", "炮孔组1", "hole-icon"),
                        new DaisyTreeData("hole-2", "炮孔组2", "hole-icon")
                    }
                },
                new DaisyTreeData("blast-2", "爆破区域2", "blast-icon")
                {
                    Children = new List<DaisyTreeData>
                    {
                        new DaisyTreeData("hole-3", "炮孔组3", "hole-icon")
                    }
                }
            };
        }
        
        private List<DaisyTreeData> CreateLayerTreeData()
        {
            // 创建图层数据（示例）
            return new List<DaisyTreeData>
            {
                new DaisyTreeData("layer-1", "地形图层", "layer-icon"),
                new DaisyTreeData("layer-2", "爆破图层", "layer-icon"),
                new DaisyTreeData("layer-3", "安全图层", "layer-icon")
            };
        }
        
        private List<DaisyTreeData> CreateMaterialTreeData()
        {
            // 创建材质数据（示例）
            return new List<DaisyTreeData>
            {
                new DaisyTreeData("mat-1", "岩石材质", "material-icon"),
                new DaisyTreeData("mat-2", "土壤材质", "material-icon"),
                new DaisyTreeData("mat-3", "爆破材质", "material-icon")
            };
        }
    }
    
    #endregion
    
    #region 扩展事件定义
    
    /// <summary>
    /// 全局搜索事件
    /// </summary>
    public class GlobalSearchEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string SearchQuery { get; }
        public string[] ComponentIds { get; }
        
        public GlobalSearchEvent(string searchQuery, params string[] componentIds)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            SearchQuery = searchQuery;
            ComponentIds = componentIds;
        }
    }
    
    /// <summary>
    /// 树选择事件
    /// </summary>
    public class TreeSelectionEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public object SelectedItem { get; }
        public bool IsMultiSelection { get; }
        
        public TreeSelectionEvent(string componentId, object selectedItem, bool isMultiSelection = false)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            SelectedItem = selectedItem;
            IsMultiSelection = isMultiSelection;
        }
    }
    
    /// <summary>
    /// UI选择事件
    /// </summary>
    public class UISelectionEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public string ComponentId { get; }
        public object SelectedItem { get; }
        
        public UISelectionEvent(string componentId, object selectedItem)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ComponentId = componentId;
            SelectedItem = selectedItem;
        }
    }
    
    /// <summary>
    /// 项目数据更新事件
    /// </summary>
    public class ProjectDataUpdateEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public ProjectDataType DataType { get; }
        public object UpdatedData { get; }
        
        public ProjectDataUpdateEvent(ProjectDataType dataType, object updatedData)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            DataType = dataType;
            UpdatedData = updatedData;
        }
    }
    
    /// <summary>
    /// 项目数据类型
    /// </summary>
    public enum ProjectDataType
    {
        BlastingDesign,
        Layers,
        Materials,
        Tools,
        Settings
    }
    
    #endregion
    
    #region 应用程序集成示例
    
    /// <summary>
    /// 应用程序主类，展示整个事件系统的集成
    /// </summary>
    public class BlastingDesignApp : MonoBehaviour
    {
        private IEventBus _eventBus;
        private TreeSearchEventHandler _searchHandler;
        private NewLeftPanel _leftPanel;
        private IEventSubscriptionManager _subscriptionManager;
        
        private void Start()
        {
            InitializeEventSystem();
            InitializeUI();
            SetupGlobalEventHandlers();
        }
        
        private void InitializeEventSystem()
        {
            // 初始化事件总线（这里使用简化的实现）
            _eventBus = new SimpleEventBus();
            
            // 初始化搜索处理器
            _searchHandler = new TreeSearchEventHandler(
                _eventBus,
                new EventSubscriptionManager(_eventBus)
            );
            
            // 创建全局订阅管理器
            _subscriptionManager = new EventSubscriptionManager(_eventBus);
        }
        
        private void InitializeUI()
        {
            // 创建左侧面板
            _leftPanel = new NewLeftPanel(_eventBus, _searchHandler);
            
            // 添加到UI根节点
            var root = GetComponent<UIDocument>().rootVisualElement;
            root.Add(_leftPanel);
        }
        
        private void SetupGlobalEventHandlers()
        {
            // 监听全局键盘快捷键
            _subscriptionManager.Subscribe<KeyboardShortcutEvent>(HandleKeyboardShortcut);
            
            // 监听性能监控事件
            _subscriptionManager.Subscribe<SearchPerformanceEvent>(HandleSearchPerformance);
            
            // 监听UI选择事件
            _subscriptionManager.Subscribe<UISelectionEvent>(HandleUISelection);
        }
        
        private void HandleKeyboardShortcut(KeyboardShortcutEvent evt)
        {
            switch (evt.ShortcutType)
            {
                case ShortcutType.GlobalSearch:
                    // 触发全局搜索
                    _eventBus.Publish(new GlobalSearchEvent(evt.SearchQuery, "blasting-tree", "layer-tree", "material-tree"));
                    break;
                    
                case ShortcutType.ClearSearch:
                    // 清除所有搜索
                    _eventBus.Publish(new GlobalSearchEvent(""));
                    break;
            }
        }
        
        private void HandleSearchPerformance(SearchPerformanceEvent evt)
        {
            // 性能监控
            if (evt.SearchDuration.TotalMilliseconds > 100)
            {
                Debug.LogWarning($"Slow search detected in {evt.ComponentId}: {evt.SearchDuration.TotalMilliseconds:F2}ms");
            }
        }
        
        private void HandleUISelection(UISelectionEvent evt)
        {
            // 处理UI选择，可能需要更新其他面板
            Debug.Log($"UI selection changed: {evt.SelectedItem} in {evt.ComponentId}");
            
            // 触发右侧属性面板更新
            _eventBus.Publish(new PropertyPanelUpdateEvent(evt.SelectedItem));
        }
        
        private void OnDestroy()
        {
            // 清理资源
            _subscriptionManager?.Dispose();
            _leftPanel?.Dispose();
        }
    }
    
    #endregion
    
    #region 简化的事件总线实现
    
    /// <summary>
    /// 简化的事件总线实现（实际应该使用完整版本）
    /// </summary>
    public class SimpleEventBus : IEventBus
    {
        private readonly Dictionary<Type, List<Delegate>> _subscriptions = new();
        private readonly Dictionary<Type, List<FilteredSubscription>> _filteredSubscriptions = new();
        
        public void Subscribe<T>(Action<T> handler) where T : IEvent
        {
            var eventType = typeof(T);
            if (!_subscriptions.ContainsKey(eventType))
                _subscriptions[eventType] = new List<Delegate>();
                
            _subscriptions[eventType].Add(handler);
        }
        
        public void Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            var eventType = typeof(T);
            if (!_filteredSubscriptions.ContainsKey(eventType))
                _filteredSubscriptions[eventType] = new List<FilteredSubscription>();
                
            _filteredSubscriptions[eventType].Add(new FilteredSubscription<T>(handler, filter));
        }
        
        public void Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            // 简化实现，实际应该考虑优先级
            Subscribe(handler);
        }
        
        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            var eventType = typeof(T);
            if (_subscriptions.ContainsKey(eventType))
            {
                _subscriptions[eventType].Remove(handler);
            }
        }
        
        public void Publish<T>(T eventData) where T : IEvent
        {
            var eventType = typeof(T);
            
            // 处理普通订阅
            if (_subscriptions.ContainsKey(eventType))
            {
                foreach (var handler in _subscriptions[eventType])
                {
                    ((Action<T>)handler).Invoke(eventData);
                }
            }
            
            // 处理过滤订阅
            if (_filteredSubscriptions.ContainsKey(eventType))
            {
                foreach (var subscription in _filteredSubscriptions[eventType])
                {
                    if (subscription is FilteredSubscription<T> filtered)
                    {
                        if (filtered.Filter(eventData))
                        {
                            filtered.Handler(eventData);
                        }
                    }
                }
            }
        }
        
        public void PublishAsync<T>(T eventData) where T : IEvent
        {
            // 简化实现，实际应该使用异步
            Publish(eventData);
        }
        
        public void PublishDelayed<T>(T eventData, float delay) where T : IEvent
        {
            // 简化实现，实际应该使用调度器
            Publish(eventData);
        }
        
        public void PublishBatch<T>(IEnumerable<T> events) where T : IEvent
        {
            foreach (var evt in events)
            {
                Publish(evt);
            }
        }
        
        public void ClearAllSubscriptions()
        {
            _subscriptions.Clear();
            _filteredSubscriptions.Clear();
        }
        
        public void ClearSubscriptions<T>() where T : IEvent
        {
            var eventType = typeof(T);
            _subscriptions.Remove(eventType);
            _filteredSubscriptions.Remove(eventType);
        }
        
        private abstract class FilteredSubscription
        {
        }
        
        private class FilteredSubscription<T> : FilteredSubscription where T : IEvent
        {
            public Action<T> Handler { get; }
            public Func<T, bool> Filter { get; }
            
            public FilteredSubscription(Action<T> handler, Func<T, bool> filter)
            {
                Handler = handler;
                Filter = filter;
            }
        }
    }
    
    #endregion
    
    #region 辅助事件定义
    
    public class KeyboardShortcutEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public ShortcutType ShortcutType { get; }
        public string SearchQuery { get; }
        
        public KeyboardShortcutEvent(ShortcutType shortcutType, string searchQuery = null)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            ShortcutType = shortcutType;
            SearchQuery = searchQuery;
        }
    }
    
    public enum ShortcutType
    {
        GlobalSearch,
        ClearSearch,
        ExpandAll,
        CollapseAll
    }
    
    public class PropertyPanelUpdateEvent : IEvent
    {
        public DateTime Timestamp { get; }
        public string EventId { get; }
        public object SelectedItem { get; }
        
        public PropertyPanelUpdateEvent(object selectedItem)
        {
            Timestamp = DateTime.Now;
            EventId = Guid.NewGuid().ToString();
            SelectedItem = selectedItem;
        }
    }
    
    #endregion
}