using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 高性能事件发布器实现
    /// </summary>
    public class EventPublisher : IEventPublisher
    {
        private readonly IEventSubscriptionManager _subscriptionManager;
        private readonly IEventErrorHandler _errorHandler;
        private readonly EventPublishStatistics _statistics;
        private readonly bool _debugMode;
        private volatile bool _disposed = false;
        
        public bool DebugMode { get; set; }
        
        public EventPublisher(
            IEventSubscriptionManager subscriptionManager,
            IEventErrorHandler errorHandler = null,
            bool debugMode = false)
        {
            _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
            _errorHandler = errorHandler;
            _debugMode = debugMode;
            DebugMode = debugMode;
            _statistics = new EventPublishStatistics();
        }
        
        public EventPublishResult Publish<T>(T eventData) where T : IEvent
        {
            if (eventData == null)
                throw new ArgumentNullException(nameof(eventData));
            
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventPublisher));
            
            var result = new EventPublishResult
            {
                EventId = eventData.EventId,
                EventType = typeof(T),
                PublishTime = DateTime.UtcNow,
                IsSuccess = true
            };
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var subscriptions = _subscriptionManager.GetSubscriptions<T>().ToList();
                result.ProcessedSubscribers = subscriptions.Count;
                
                if (DebugMode)
                {
                    UnityEngine.Debug.Log($"EventPublisher: 发布事件 {typeof(T).Name}，订阅者数量: {subscriptions.Count}");
                }
                
                foreach (var subscription in subscriptions)
                {
                    if (!subscription.IsValid) continue;
                    
                    try
                    {
                        subscription.Invoke(eventData);
                        
                        if (eventData.IsCancelled)
                        {
                            result.IsCancelled = true;
                            if (DebugMode)
                            {
                                UnityEngine.Debug.Log($"EventPublisher: 事件 {typeof(T).Name} 被取消");
                            }
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.IsSuccess = false;
                        result.Exception = ex;
                        
                        if (_errorHandler != null)
                        {
                            var context = new EventErrorContext
                            {
                                Stage = EventErrorStage.DuringProcessing,
                                Subscription = subscription,
                                ErrorTime = DateTime.UtcNow,
                                RetryCount = 0,
                                MaxRetries = 3
                            };
                            
                            var errorResult = _errorHandler.HandleError(eventData, ex, context).Result;
                            if (errorResult.ShouldStopProcessing)
                            {
                                break;
                            }
                        }
                        else
                        {
                            UnityEngine.Debug.LogError($"EventPublisher: 处理事件 {typeof(T).Name} 时发生错误: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Exception = ex;
                UnityEngine.Debug.LogError($"EventPublisher: 发布事件 {typeof(T).Name} 时发生错误: {ex.Message}");
            }
            finally
            {
                stopwatch.Stop();
                result.ProcessingTime = stopwatch.Elapsed.TotalMilliseconds;
                
                // 更新统计信息
                _statistics.RecordPublish(result);
            }
            
            return result;
        }
        
        public async Task<EventPublishResult> PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventPublisher));
            
            return await Task.Run(() => Publish(eventData), cancellationToken);
        }
        
        public async Task<EventPublishResult> PublishDelayed<T>(T eventData, TimeSpan delay, CancellationToken cancellationToken = default) where T : IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventPublisher));
            
            await Task.Delay(delay, cancellationToken);
            return await PublishAsync(eventData, cancellationToken);
        }
        
        public async Task<IEnumerable<EventPublishResult>> PublishBatch<T>(IEnumerable<T> events, CancellationToken cancellationToken = default) where T : IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventPublisher));
            
            var results = new List<EventPublishResult>();
            
            foreach (var eventData in events)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var result = await PublishAsync(eventData, cancellationToken);
                results.Add(result);
            }
            
            return results;
        }
        
        public IEventPublishStatistics GetPublishStatistics()
        {
            return _statistics;
        }
        
        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            // 清理资源
            _statistics?.Dispose();
        }
    }
    
    /// <summary>
    /// 事件发布统计信息实现
    /// </summary>
    internal class EventPublishStatistics : IEventPublishStatistics, IDisposable
    {
        private readonly ConcurrentDictionary<Type, EventTypePublishStatistics> _typeStatistics;
        private readonly object _lockObject = new object();
        
        private long _totalPublishedEvents = 0;
        private long _successfulPublishedEvents = 0;
        private long _failedPublishedEvents = 0;
        private double _totalProcessingTime = 0;
        private double _maxProcessingTime = 0;
        private double _minProcessingTime = double.MaxValue;
        
        public long TotalPublishedEvents => _totalPublishedEvents;
        public long SuccessfulPublishedEvents => _successfulPublishedEvents;
        public long FailedPublishedEvents => _failedPublishedEvents;
        
        public double AverageProcessingTime
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalPublishedEvents > 0 ? _totalProcessingTime / _totalPublishedEvents : 0;
                }
            }
        }
        
        public double MaxProcessingTime => _maxProcessingTime;
        public double MinProcessingTime => _minProcessingTime == double.MaxValue ? 0 : _minProcessingTime;
        
        public EventPublishStatistics()
        {
            _typeStatistics = new ConcurrentDictionary<Type, EventTypePublishStatistics>();
        }
        
        public void RecordPublish(EventPublishResult result)
        {
            lock (_lockObject)
            {
                _totalPublishedEvents++;
                _totalProcessingTime += result.ProcessingTime;
                
                if (result.IsSuccess)
                {
                    _successfulPublishedEvents++;
                }
                else
                {
                    _failedPublishedEvents++;
                }
                
                if (result.ProcessingTime > _maxProcessingTime)
                {
                    _maxProcessingTime = result.ProcessingTime;
                }
                
                if (result.ProcessingTime < _minProcessingTime)
                {
                    _minProcessingTime = result.ProcessingTime;
                }
            }
            
            // 更新类型统计
            var typeStats = _typeStatistics.GetOrAdd(result.EventType, 
                type => new EventTypePublishStatistics(type));
            typeStats.RecordPublish(result);
        }
        
        public IEventTypePublishStatistics GetEventTypeStatistics(Type eventType)
        {
            return _typeStatistics.GetOrAdd(eventType, 
                type => new EventTypePublishStatistics(type));
        }
        
        public void Dispose()
        {
            _typeStatistics?.Clear();
        }
    }
    
    /// <summary>
    /// 事件类型发布统计信息实现
    /// </summary>
    internal class EventTypePublishStatistics : IEventTypePublishStatistics
    {
        private readonly object _lockObject = new object();
        
        public Type EventType { get; }
        
        private long _publishCount = 0;
        private long _successfulPublishCount = 0;
        private long _failedPublishCount = 0;
        private double _totalProcessingTime = 0;
        private DateTime _lastPublishTime = DateTime.MinValue;
        
        public long PublishCount => _publishCount;
        public long SuccessfulPublishCount => _successfulPublishCount;
        public long FailedPublishCount => _failedPublishCount;
        public DateTime LastPublishTime => _lastPublishTime;
        
        public double AverageProcessingTime
        {
            get
            {
                lock (_lockObject)
                {
                    return _publishCount > 0 ? _totalProcessingTime / _publishCount : 0;
                }
            }
        }
        
        public EventTypePublishStatistics(Type eventType)
        {
            EventType = eventType;
        }
        
        public void RecordPublish(EventPublishResult result)
        {
            lock (_lockObject)
            {
                _publishCount++;
                _totalProcessingTime += result.ProcessingTime;
                _lastPublishTime = result.PublishTime;
                
                if (result.IsSuccess)
                {
                    _successfulPublishCount++;
                }
                else
                {
                    _failedPublishCount++;
                }
            }
        }
    }
}