# 工具栏堆栈溢出修复使用指南

## 修复概述

已成功修复工具栏中PublishEvent方法导致的堆栈溢出问题。修复包括：

1. **重入保护机制** - 防止递归调用
2. **事件发布优化** - 分离事件发布逻辑
3. **错误处理增强** - 更好的异常处理和日志记录
4. **测试验证工具** - 专门的测试脚本

## 如何验证修复

### 1. 使用测试脚本

在Unity中添加`ToolbarStackOverflowTest`组件到任意GameObject：

```csharp
// 在Inspector中设置
enableTest = true;
enableDetailedLogging = true;
maxTestIterations = 10;
```

或者通过右键菜单：
- 右键点击组件 → "开始堆栈溢出测试"

### 2. 手动测试

1. 打开包含工具栏的场景
2. 快速连续点击不同的工具按钮
3. 观察控制台日志，应该看到：
   - 正常的工具选择日志
   - 如果有重入尝试，会看到警告："检测到工具选择重入调用，忽略"

### 3. 监控日志

关注以下日志信息：

**正常操作日志**：
```
[Toolbar] 工具按钮点击: 选择工具
[StatusBar] 选择工具已激活
```

**重入保护日志**：
```
[Toolbar] 检测到工具选择重入调用，忽略: tool-name
[Toolbar] 检测到切换按钮重入调用，忽略: toggle-tool
```

**错误处理日志**：
```
[UIElementBase] 发布事件时发生堆栈溢出: ToolStateChangedEvent，可能存在循环调用
[Toolbar] 发布工具选择事件时出错: 异常信息
```

## 修复的关键特性

### 1. 重入保护

```csharp
private bool isProcessingToolSelection = false;

private void HandleToolSelection(...)
{
    if (isProcessingToolSelection)
    {
        // 阻止重入调用
        return;
    }
    
    try
    {
        isProcessingToolSelection = true;
        // 处理逻辑
    }
    finally
    {
        isProcessingToolSelection = false;
    }
}
```

### 2. 分离的事件发布

- `PublishToolSelectionEvents()` - 处理工具选择相关事件
- `PublishToolButtonClickedEvents()` - 处理按钮点击事件
- 事件发布在状态变更完成后进行，避免中间状态的问题

### 3. 增强的错误处理

- 专门的堆栈溢出异常检测
- 详细的错误日志和堆栈跟踪
- 优雅的错误恢复机制

## 性能影响

修复对性能的影响极小：

- **重入检查** - 简单的布尔值检查，几乎无性能开销
- **事件发布分离** - 不增加额外的事件数量，只是重新组织
- **错误处理** - 只在异常情况下执行，正常情况无影响

## 兼容性

修复完全向后兼容：

- 保持所有现有的事件类型和接口
- 新旧事件系统并存
- 不影响现有的事件处理器

## 故障排除

### 如果仍然出现堆栈溢出

1. **检查自定义事件处理器**：
   ```csharp
   // 避免在事件处理器中直接调用工具栏方法
   private void OnToolStateChanged(ToolStateChangedEvent evt)
   {
       // ❌ 错误：可能导致循环调用
       // toolbar.SelectTool("another-tool");
       
       // ✅ 正确：只更新UI状态
       UpdateUIState(evt.ToolName, evt.CurrentState);
   }
   ```

2. **检查回调系统**：
   确保工具按钮的回调不会触发新的工具选择

3. **启用详细日志**：
   ```csharp
   // 在Toolbar组件中
   debugMode = true;
   
   // 在测试脚本中
   enableDetailedLogging = true;
   ```

### 如果重入保护过于严格

如果发现合法的工具选择被阻止：

1. 检查调用时机 - 确保不在事件处理过程中调用
2. 使用延迟调用：
   ```csharp
   // 延迟到下一帧执行
   StartCoroutine(DelayedToolSelection("tool-name"));
   ```

## 最佳实践

### 事件处理器编写

1. **避免循环调用**：
   ```csharp
   private void OnToolSelected(ToolSelectedEvent evt)
   {
       // ✅ 只更新UI，不触发新的工具操作
       UpdateToolbarUI(evt.ToolName);
   }
   ```

2. **检查事件来源**：
   ```csharp
   private void OnToolStateChanged(ToolStateChangedEvent evt)
   {
       // 忽略测试事件，避免处理测试产生的事件
       if (evt.Source == "StackOverflowTest") return;
       
       // 处理正常事件
       HandleToolStateChange(evt);
   }
   ```

3. **使用异步处理**：
   ```csharp
   private async void OnComplexEvent(SomeEvent evt)
   {
       // 对于复杂的处理逻辑，使用异步方式
       await ProcessEventAsync(evt);
   }
   ```

## 总结

堆栈溢出问题已通过重入保护机制得到根本解决。修复方案：

- ✅ 防止递归调用导致的堆栈溢出
- ✅ 保持完整的功能性和兼容性
- ✅ 提供详细的调试和监控工具
- ✅ 包含完整的测试验证机制

如有任何问题，请查看日志输出或运行测试脚本进行诊断。
