using UnityEngine;
using UnityEditor;
using BlastingDesign.Events.Migration;

namespace BlastingDesign.Events.Migration.Editor
{
    /// <summary>
    /// 事件系统迁移管理器窗口
    /// 提供可视化界面来管理和监控迁移过程
    /// </summary>
    public class MigrationManagerWindow : EditorWindow
    {
        private MigrationTracker _migrationTracker;
        private Vector2 _scrollPosition;
        private bool _showCompletedItems = true;
        private bool _showInProgressItems = true;
        private bool _showFailedItems = true;
        private bool _showNotStartedItems = true;

        [MenuItem("Tools/Event System/Migration Manager")]
        public static void ShowWindow()
        {
            GetWindow<MigrationManagerWindow>("Event System Migration Manager");
        }

        private void OnEnable()
        {
            _migrationTracker = new MigrationTracker();
            titleContent = new GUIContent("Migration Manager");
        }

        private void OnGUI()
        {
            DrawHeader();
            DrawMigrationControls();
            DrawProgressOverview();
            DrawMigrationItems();
            DrawReportSection();
        }

        private void DrawHeader()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField("事件系统迁移管理器", EditorStyles.boldLabel);
            GUILayout.Space(5);
            
            EditorGUILayout.HelpBox(
                "此工具用于管理和监控事件系统从旧UnityEvent系统到新EventBus系统的迁移过程。", 
                MessageType.Info);
        }

        private void DrawMigrationControls()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField("迁移控制", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("开始迁移", GUILayout.Width(100)))
            {
                _migrationTracker.StartMigration();
            }
            
            if (GUILayout.Button("重置迁移", GUILayout.Width(100)))
            {
                _migrationTracker = new MigrationTracker();
            }
            
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(5);
            EditorGUILayout.LabelField($"当前阶段: {_migrationTracker.CurrentPhase}");
            
            if (_migrationTracker.CurrentPhase != MigrationPhase.NotStarted)
            {
                EditorGUILayout.LabelField($"开始时间: {_migrationTracker.MigrationStartTime}");
            }
        }

        private void DrawProgressOverview()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField("进度概览", EditorStyles.boldLabel);
            
            var report = _migrationTracker.GenerateReport();
            
            // 进度条
            var progressRect = GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true));
            EditorGUI.ProgressBar(progressRect, report.ProgressPercentage / 100f, 
                $"{report.CompletedCount}/{report.TotalCount} ({report.ProgressPercentage:F1}%)");
            
            // 状态统计
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"完成: {report.CompletedCount}", GUILayout.Width(80));
            EditorGUILayout.LabelField($"进行中: {report.InProgressCount}", GUILayout.Width(80));
            EditorGUILayout.LabelField($"失败: {report.FailedCount}", GUILayout.Width(80));
            EditorGUILayout.LabelField($"未开始: {report.TotalCount - report.CompletedCount - report.InProgressCount - report.FailedCount}");
            EditorGUILayout.EndHorizontal();
        }

        private void DrawMigrationItems()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField("迁移项目", EditorStyles.boldLabel);
            
            // 过滤选项
            EditorGUILayout.BeginHorizontal();
            _showCompletedItems = EditorGUILayout.ToggleLeft("显示已完成", _showCompletedItems, GUILayout.Width(100));
            _showInProgressItems = EditorGUILayout.ToggleLeft("显示进行中", _showInProgressItems, GUILayout.Width(100));
            _showFailedItems = EditorGUILayout.ToggleLeft("显示失败", _showFailedItems, GUILayout.Width(100));
            _showNotStartedItems = EditorGUILayout.ToggleLeft("显示未开始", _showNotStartedItems, GUILayout.Width(100));
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(5);
            
            // 滚动视图
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            foreach (MigrationPhase phase in System.Enum.GetValues(typeof(MigrationPhase)))
            {
                if (phase == MigrationPhase.NotStarted) continue;
                
                var phaseItems = _migrationTracker.GetItemsByPhase(phase);
                if (phaseItems.Count == 0) continue;
                
                // 阶段标题
                EditorGUILayout.LabelField($"--- {GetPhaseDisplayName(phase)} ---", EditorStyles.boldLabel);
                
                foreach (var item in phaseItems)
                {
                    if (!ShouldShowItem(item)) continue;
                    
                    DrawMigrationItem(item);
                }
                
                GUILayout.Space(5);
            }
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawMigrationItem(MigrationItem item)
        {
            EditorGUILayout.BeginHorizontal();
            
            // 状态图标
            var statusIcon = GetStatusIcon(item.Status);
            var statusColor = GetStatusColor(item.Status);
            
            var originalColor = GUI.color;
            GUI.color = statusColor;
            EditorGUILayout.LabelField(statusIcon, GUILayout.Width(20));
            GUI.color = originalColor;
            
            // 项目名称
            EditorGUILayout.LabelField(item.Name, GUILayout.ExpandWidth(true));
            
            // 优先级
            var priorityColor = GetPriorityColor(item.Priority);
            GUI.color = priorityColor;
            EditorGUILayout.LabelField($"[{item.Priority}]", GUILayout.Width(60));
            GUI.color = originalColor;
            
            // 操作按钮
            if (item.Status == MigrationStatus.NotStarted)
            {
                if (GUILayout.Button("开始", GUILayout.Width(50)))
                {
                    _migrationTracker.MarkItemInProgress(item.Name);
                }
            }
            else if (item.Status == MigrationStatus.InProgress)
            {
                if (GUILayout.Button("完成", GUILayout.Width(50)))
                {
                    _migrationTracker.MarkItemCompleted(item.Name);
                }
                if (GUILayout.Button("失败", GUILayout.Width(50)))
                {
                    var errorMessage = EditorUtility.DisplayDialog(
                        "标记为失败", 
                        "请输入失败原因:", 
                        "确定", 
                        "取消") ? "手动标记为失败" : "";
                    
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        _migrationTracker.MarkItemFailed(item.Name, errorMessage);
                    }
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            // 显示错误信息
            if (!string.IsNullOrEmpty(item.ErrorMessage))
            {
                EditorGUILayout.HelpBox($"错误: {item.ErrorMessage}", MessageType.Error);
            }
            
            // 显示时间信息
            if (item.Status != MigrationStatus.NotStarted)
            {
                var duration = item.GetDuration();
                EditorGUILayout.LabelField($"    耗时: {duration.TotalHours:F1}小时", EditorStyles.miniLabel);
            }
        }

        private void DrawReportSection()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField("报告", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("生成详细报告", GUILayout.Width(120)))
            {
                var report = _migrationTracker.GenerateReport();
                var detailedReport = report.GenerateDetailedReport();
                
                var path = EditorUtility.SaveFilePanel("保存迁移报告", "", "migration_report.txt", "txt");
                if (!string.IsNullOrEmpty(path))
                {
                    System.IO.File.WriteAllText(path, detailedReport);
                    EditorUtility.DisplayDialog("报告已保存", $"迁移报告已保存到: {path}", "确定");
                }
            }
            
            if (GUILayout.Button("显示进度摘要", GUILayout.Width(120)))
            {
                var summary = _migrationTracker.GetProgressSummary();
                EditorUtility.DisplayDialog("进度摘要", summary, "确定");
            }
            
            EditorGUILayout.EndHorizontal();
        }

        private bool ShouldShowItem(MigrationItem item)
        {
            switch (item.Status)
            {
                case MigrationStatus.Completed:
                    return _showCompletedItems;
                case MigrationStatus.InProgress:
                    return _showInProgressItems;
                case MigrationStatus.Failed:
                    return _showFailedItems;
                case MigrationStatus.NotStarted:
                    return _showNotStartedItems;
                default:
                    return true;
            }
        }

        private string GetStatusIcon(MigrationStatus status)
        {
            switch (status)
            {
                case MigrationStatus.Completed: return "✅";
                case MigrationStatus.InProgress: return "🔄";
                case MigrationStatus.Failed: return "❌";
                default: return "⏳";
            }
        }

        private Color GetStatusColor(MigrationStatus status)
        {
            switch (status)
            {
                case MigrationStatus.Completed: return Color.green;
                case MigrationStatus.InProgress: return Color.yellow;
                case MigrationStatus.Failed: return Color.red;
                default: return Color.gray;
            }
        }

        private Color GetPriorityColor(MigrationPriority priority)
        {
            switch (priority)
            {
                case MigrationPriority.High: return Color.red;
                case MigrationPriority.Medium: return Color.yellow;
                case MigrationPriority.Low: return Color.green;
                default: return Color.white;
            }
        }

        private string GetPhaseDisplayName(MigrationPhase phase)
        {
            switch (phase)
            {
                case MigrationPhase.Preparation: return "准备和设计";
                case MigrationPhase.CoreUIMigration: return "核心UI组件迁移";
                case MigrationPhase.InputSystemIntegration: return "输入系统整合";
                case MigrationPhase.DaisyUIMigration: return "DaisyUI组件迁移";
                case MigrationPhase.CleanupAndOptimization: return "系统清理和优化";
                default: return phase.ToString();
            }
        }
    }
}