using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.Events.Services
{
    /// <summary>
    /// 事件性能监控器
    /// 用于监控和分析事件系统的性能表现
    /// </summary>
    public class EventPerformanceMonitor : IDisposable
    {
        private readonly ConcurrentDictionary<Type, EventTypePerformanceData> _performanceData;
        private readonly ConcurrentQueue<EventExecutionRecord> _executionHistory;
        private readonly object _lockObject = new object();

        // 配置
        private readonly int _maxHistoryRecords;
        private readonly bool _enableDetailedLogging;

        // 统计信息
        private long _totalEventsProcessed = 0;
        private double _totalProcessingTime = 0;
        private DateTime _monitoringStartTime;

        public EventPerformanceMonitor(int maxHistoryRecords = 1000, bool enableDetailedLogging = false)
        {
            _performanceData = new ConcurrentDictionary<Type, EventTypePerformanceData>();
            _executionHistory = new ConcurrentQueue<EventExecutionRecord>();
            _maxHistoryRecords = maxHistoryRecords;
            _enableDetailedLogging = enableDetailedLogging;
            _monitoringStartTime = DateTime.UtcNow;
        }

        /// <summary>
        /// 记录事件执行性能
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="executionTime">执行时间（毫秒）</param>
        /// <param name="subscriberCount">订阅者数量</param>
        /// <param name="source">事件源</param>
        public void RecordEventExecution(Type eventType, double executionTime, int subscriberCount, string source = null)
        {
            _totalEventsProcessed++;
            _totalProcessingTime += executionTime;

            // 更新事件类型性能数据
            var performanceData = _performanceData.GetOrAdd(eventType, type => new EventTypePerformanceData(type));
            performanceData.RecordExecution(executionTime, subscriberCount);

            // 添加到执行历史
            var record = new EventExecutionRecord
            {
                EventType = eventType,
                ExecutionTime = executionTime,
                SubscriberCount = subscriberCount,
                Timestamp = DateTime.UtcNow,
                Source = source
            };

            _executionHistory.Enqueue(record);

            // 限制历史记录数量
            while (_executionHistory.Count > _maxHistoryRecords)
            {
                _executionHistory.TryDequeue(out _);
            }

            // 详细日志记录
            if (_enableDetailedLogging)
            {
                UnityEngine.Debug.Log($"EventPerformanceMonitor: {eventType.Name} 执行时间: {executionTime:F2}ms, 订阅者: {subscriberCount}");
            }

            // 性能警告
            if (executionTime > 10.0) // 超过10ms的事件处理
            {
                UnityEngine.Debug.LogWarning($"EventPerformanceMonitor: 事件 {eventType.Name} 处理时间过长: {executionTime:F2}ms");
            }
        }

        /// <summary>
        /// 获取整体性能统计
        /// </summary>
        /// <returns>整体性能统计</returns>
        public OverallPerformanceStatistics GetOverallStatistics()
        {
            lock (_lockObject)
            {
                var runningTime = DateTime.UtcNow - _monitoringStartTime;

                return new OverallPerformanceStatistics
                {
                    TotalEventsProcessed = _totalEventsProcessed,
                    TotalProcessingTime = _totalProcessingTime,
                    AverageProcessingTime = _totalEventsProcessed > 0 ? _totalProcessingTime / _totalEventsProcessed : 0,
                    EventsPerSecond = runningTime.TotalSeconds > 0 ? _totalEventsProcessed / runningTime.TotalSeconds : 0,
                    MonitoringDuration = runningTime,
                    ActiveEventTypes = _performanceData.Count,
                    HistoryRecordCount = _executionHistory.Count
                };
            }
        }

        /// <summary>
        /// 获取指定事件类型的性能统计
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型性能统计</returns>
        public EventTypePerformanceStatistics GetEventTypeStatistics(Type eventType)
        {
            if (_performanceData.TryGetValue(eventType, out EventTypePerformanceData data))
            {
                return data.GetStatistics();
            }

            return null;
        }

        /// <summary>
        /// 获取所有事件类型的性能统计
        /// </summary>
        /// <returns>所有事件类型性能统计</returns>
        public List<EventTypePerformanceStatistics> GetAllEventTypeStatistics()
        {
            return _performanceData.Values
                .Select(data => data.GetStatistics())
                .OrderByDescending(stats => stats.TotalExecutionTime)
                .ToList();
        }

        /// <summary>
        /// 获取性能最差的事件类型
        /// </summary>
        /// <param name="topCount">返回数量</param>
        /// <returns>性能最差的事件类型列表</returns>
        public List<EventTypePerformanceStatistics> GetWorstPerformingEvents(int topCount = 10)
        {
            return GetAllEventTypeStatistics()
                .OrderByDescending(stats => stats.AverageExecutionTime)
                .Take(topCount)
                .ToList();
        }

        /// <summary>
        /// 获取最频繁的事件类型
        /// </summary>
        /// <param name="topCount">返回数量</param>
        /// <returns>最频繁的事件类型列表</returns>
        public List<EventTypePerformanceStatistics> GetMostFrequentEvents(int topCount = 10)
        {
            return GetAllEventTypeStatistics()
                .OrderByDescending(stats => stats.ExecutionCount)
                .Take(topCount)
                .ToList();
        }

        /// <summary>
        /// 获取最近的执行记录
        /// </summary>
        /// <param name="count">记录数量</param>
        /// <returns>最近的执行记录</returns>
        public List<EventExecutionRecord> GetRecentExecutions(int count = 100)
        {
            return _executionHistory.ToArray()
                .OrderByDescending(record => record.Timestamp)
                .Take(count)
                .ToList();
        }

        /// <summary>
        /// 重置所有统计数据
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _performanceData.Clear();
                while (_executionHistory.TryDequeue(out _)) { }
                _totalEventsProcessed = 0;
                _totalProcessingTime = 0;
                _monitoringStartTime = DateTime.UtcNow;

                if (_enableDetailedLogging)
                {
                    UnityEngine.Debug.Log("EventPerformanceMonitor: 统计数据已重置");
                }
            }
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <returns>性能报告字符串</returns>
        public string GeneratePerformanceReport()
        {
            var overall = GetOverallStatistics();
            var worstEvents = GetWorstPerformingEvents(5);
            var frequentEvents = GetMostFrequentEvents(5);

            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 事件系统性能报告 ===");
            report.AppendLine($"监控时长: {overall.MonitoringDuration.TotalMinutes:F1} 分钟");
            report.AppendLine($"总处理事件: {overall.TotalEventsProcessed}");
            report.AppendLine($"平均处理时间: {overall.AverageProcessingTime:F2} ms");
            report.AppendLine($"事件处理频率: {overall.EventsPerSecond:F1} 事件/秒");
            report.AppendLine($"活跃事件类型: {overall.ActiveEventTypes}");
            report.AppendLine();

            report.AppendLine("=== 性能最差的事件类型 ===");
            foreach (var eventStats in worstEvents)
            {
                report.AppendLine($"{eventStats.EventType.Name}: 平均 {eventStats.AverageExecutionTime:F2}ms, 执行 {eventStats.ExecutionCount} 次");
            }
            report.AppendLine();

            report.AppendLine("=== 最频繁的事件类型 ===");
            foreach (var eventStats in frequentEvents)
            {
                report.AppendLine($"{eventStats.EventType.Name}: 执行 {eventStats.ExecutionCount} 次, 平均 {eventStats.AverageExecutionTime:F2}ms");
            }

            return report.ToString();
        }

        public void Dispose()
        {
            _performanceData.Clear();
            while (_executionHistory.TryDequeue(out _)) { }
        }
    }

    /// <summary>
    /// 事件类型性能数据
    /// </summary>
    internal class EventTypePerformanceData
    {
        private readonly Type _eventType;
        private readonly object _lockObject = new object();

        private long _executionCount = 0;
        private double _totalExecutionTime = 0;
        private double _minExecutionTime = double.MaxValue;
        private double _maxExecutionTime = double.MinValue;
        private DateTime _lastExecutionTime = DateTime.MinValue;
        private int _totalSubscribers = 0;

        public EventTypePerformanceData(Type eventType)
        {
            _eventType = eventType;
        }

        public void RecordExecution(double executionTime, int subscriberCount)
        {
            lock (_lockObject)
            {
                _executionCount++;
                _totalExecutionTime += executionTime;
                _totalSubscribers += subscriberCount;
                _lastExecutionTime = DateTime.UtcNow;

                if (executionTime < _minExecutionTime)
                    _minExecutionTime = executionTime;

                if (executionTime > _maxExecutionTime)
                    _maxExecutionTime = executionTime;
            }
        }

        public EventTypePerformanceStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                return new EventTypePerformanceStatistics
                {
                    EventType = _eventType,
                    ExecutionCount = _executionCount,
                    TotalExecutionTime = _totalExecutionTime,
                    AverageExecutionTime = _executionCount > 0 ? _totalExecutionTime / _executionCount : 0,
                    MinExecutionTime = _minExecutionTime == double.MaxValue ? 0 : _minExecutionTime,
                    MaxExecutionTime = _maxExecutionTime == double.MinValue ? 0 : _maxExecutionTime,
                    AverageSubscriberCount = _executionCount > 0 ? (double)_totalSubscribers / _executionCount : 0,
                    LastExecutionTime = _lastExecutionTime
                };
            }
        }
    }

    #region 性能统计数据结构

    /// <summary>
    /// 整体性能统计
    /// </summary>
    public class OverallPerformanceStatistics
    {
        public long TotalEventsProcessed { get; set; }
        public double TotalProcessingTime { get; set; }
        public double AverageProcessingTime { get; set; }
        public double EventsPerSecond { get; set; }
        public TimeSpan MonitoringDuration { get; set; }
        public int ActiveEventTypes { get; set; }
        public int HistoryRecordCount { get; set; }
    }

    /// <summary>
    /// 事件类型性能统计
    /// </summary>
    public class EventTypePerformanceStatistics
    {
        public Type EventType { get; set; }
        public long ExecutionCount { get; set; }
        public double TotalExecutionTime { get; set; }
        public double AverageExecutionTime { get; set; }
        public double MinExecutionTime { get; set; }
        public double MaxExecutionTime { get; set; }
        public double AverageSubscriberCount { get; set; }
        public DateTime LastExecutionTime { get; set; }
    }

    /// <summary>
    /// 事件执行记录
    /// </summary>
    public class EventExecutionRecord
    {
        public Type EventType { get; set; }
        public double ExecutionTime { get; set; }
        public int SubscriberCount { get; set; }
        public DateTime Timestamp { get; set; }
        public string Source { get; set; }
    }

    #endregion
}
