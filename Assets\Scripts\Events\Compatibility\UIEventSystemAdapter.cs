using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;
using UnityEngine;

namespace BlastingDesign.Events.Compatibility
{
    /// <summary>
    /// UIEventSystem适配器
    /// 提供新旧事件系统之间的兼容性桥梁
    /// </summary>
    public class UIEventSystemAdapter : IEventBus
    {
        private readonly UIEventSystem _legacySystem;
        private readonly IEventBus _newSystem;
        private readonly bool _enableDebugLogging;

        // 事件类型映射
        private readonly Dictionary<Type, Func<IEvent, bool>> _legacyEventHandlers;

        public bool DebugMode
        {
            get => _newSystem.DebugMode;
            set => _newSystem.DebugMode = value;
        }

        public UIEventSystemAdapter(UIEventSystem legacySystem, IEventBus newSystem, bool enableDebugLogging = false)
        {
            if (legacySystem == null) throw new ArgumentNullException(nameof(legacySystem));
            if (newSystem == null) throw new ArgumentNullException(nameof(newSystem));

            _legacySystem = legacySystem;
            _newSystem = newSystem;
            _enableDebugLogging = enableDebugLogging;

            _legacyEventHandlers = new Dictionary<Type, Func<IEvent, bool>>();
            InitializeLegacyEventHandlers();
        }

        private void InitializeLegacyEventHandlers()
        {
            // 初始化工具栏事件映射
            InitializeToolbarEventHandlers();

            // 初始化面板事件映射
            InitializePanelEventHandlers();

            // 初始化选择事件映射
            InitializeSelectionEventHandlers();

            // 初始化状态栏事件映射
            InitializeStatusEventHandlers();

            // 初始化属性事件映射
            InitializePropertyEventHandlers();

            // 初始化自定义事件映射
            InitializeCustomEventHandlers();

            if (_enableDebugLogging)
            {
                Debug.Log($"UIEventSystemAdapter: 初始化了 {_legacyEventHandlers.Count} 个事件类型映射");
            }
        }

        /// <summary>
        /// 初始化工具栏事件映射
        /// </summary>
        private void InitializeToolbarEventHandlers()
        {
            // 菜单项点击事件映射
            _legacyEventHandlers[typeof(MenuItemClickedEvent)] = (evt) =>
            {
                if (evt is MenuItemClickedEvent menuEvent)
                {
                    _legacySystem.Toolbar.OnMenuItemClicked?.Invoke(menuEvent.MenuItem);
                    return true;
                }
                return false;
            };

            // 工具选择事件映射
            _legacyEventHandlers[typeof(ToolSelectedEvent)] = (evt) =>
            {
                if (evt is ToolSelectedEvent toolEvent)
                {
                    _legacySystem.Toolbar.OnToolSelected?.Invoke(toolEvent.ToolName);
                    return true;
                }
                return false;
            };

            // 工具栏可见性变更事件映射
            _legacyEventHandlers[typeof(ToolbarVisibilityChangedEvent)] = (evt) =>
            {
                if (evt is ToolbarVisibilityChangedEvent visibilityEvent)
                {
                    _legacySystem.Toolbar.OnToolbarVisibilityChanged?.Invoke(visibilityEvent.IsVisible);
                    return true;
                }
                return false;
            };
        }

        /// <summary>
        /// 初始化面板事件映射
        /// </summary>
        private void InitializePanelEventHandlers()
        {
            // 面板折叠事件映射
            _legacyEventHandlers[typeof(PanelCollapsedEvent)] = (evt) =>
            {
                if (evt is PanelCollapsedEvent collapseEvent)
                {
                    _legacySystem.Panel.OnPanelCollapsed?.Invoke(collapseEvent.PanelName, collapseEvent.IsCollapsed);
                    return true;
                }
                return false;
            };

            // 面板调整大小事件映射
            _legacyEventHandlers[typeof(PanelResizedEvent)] = (evt) =>
            {
                if (evt is PanelResizedEvent resizeEvent)
                {
                    _legacySystem.Panel.OnPanelResized?.Invoke(resizeEvent.PanelName, resizeEvent.NewSize);
                    return true;
                }
                return false;
            };

            // 面板可见性变更事件映射
            _legacyEventHandlers[typeof(PanelVisibilityChangedEvent)] = (evt) =>
            {
                if (evt is PanelVisibilityChangedEvent visibilityEvent)
                {
                    _legacySystem.Panel.OnPanelVisibilityChanged?.Invoke(visibilityEvent.PanelName, visibilityEvent.IsVisible);
                    return true;
                }
                return false;
            };

            // 通用面板事件映射（向后兼容）
            _legacyEventHandlers[typeof(PanelEvent)] = (evt) =>
            {
                if (evt is PanelEvent panelEvent)
                {
                    // 根据动作类型映射到具体的旧系统事件
                    switch (panelEvent.Action)
                    {
                        case PanelAction.Show:
                            _legacySystem.Panel.OnPanelVisibilityChanged?.Invoke(panelEvent.PanelName, true);
                            break;
                        case PanelAction.Hide:
                            _legacySystem.Panel.OnPanelVisibilityChanged?.Invoke(panelEvent.PanelName, false);
                            break;
                        case PanelAction.Toggle:
                            // 对于Toggle，我们需要从数据中获取当前状态
                            if (panelEvent.Data is bool isVisible)
                            {
                                _legacySystem.Panel.OnPanelVisibilityChanged?.Invoke(panelEvent.PanelName, isVisible);
                            }
                            break;
                    }

                    if (_enableDebugLogging)
                    {
                        Debug.Log($"UIEventSystemAdapter: 面板事件 {panelEvent.Action} - {panelEvent.PanelName}");
                    }
                    return true;
                }
                return false;
            };
        }

        /// <summary>
        /// 初始化选择事件映射
        /// </summary>
        private void InitializeSelectionEventHandlers()
        {
            // 对象选择事件映射
            _legacyEventHandlers[typeof(ObjectSelectedEvent)] = (evt) =>
            {
                if (evt is ObjectSelectedEvent selectionEvent)
                {
                    _legacySystem.Selection.OnObjectSelected?.Invoke(selectionEvent.SelectedObject);
                    return true;
                }
                return false;
            };

            // 多对象选择事件映射
            _legacyEventHandlers[typeof(MultipleObjectsSelectedEvent)] = (evt) =>
            {
                if (evt is MultipleObjectsSelectedEvent multiSelectionEvent)
                {
                    _legacySystem.Selection.OnMultipleObjectsSelected?.Invoke(multiSelectionEvent.SelectedObjects);
                    return true;
                }
                return false;
            };

            // 选择清除事件映射
            _legacyEventHandlers[typeof(SelectionClearedEvent)] = (evt) =>
            {
                if (evt is SelectionClearedEvent)
                {
                    _legacySystem.Selection.OnSelectionCleared?.Invoke();
                    return true;
                }
                return false;
            };
        }

        /// <summary>
        /// 初始化状态栏事件映射
        /// </summary>
        private void InitializeStatusEventHandlers()
        {
            // 状态消息事件映射
            _legacyEventHandlers[typeof(StatusMessageEvent)] = (evt) =>
            {
                if (evt is StatusMessageEvent statusEvent)
                {
                    _legacySystem.Status.OnStatusMessageChanged?.Invoke(statusEvent.Message);
                    return true;
                }
                return false;
            };

            // 鼠标世界坐标变更事件映射
            _legacyEventHandlers[typeof(MouseWorldPositionChangedEvent)] = (evt) =>
            {
                if (evt is MouseWorldPositionChangedEvent positionEvent)
                {
                    _legacySystem.Status.OnMouseWorldPositionChanged?.Invoke(positionEvent.WorldPosition);
                    return true;
                }
                return false;
            };

            // 鼠标地理坐标变更事件映射
            _legacyEventHandlers[typeof(MouseGeoPositionChangedEvent)] = (evt) =>
            {
                if (evt is MouseGeoPositionChangedEvent geoEvent)
                {
                    _legacySystem.Status.OnMouseGeoPositionChanged?.Invoke(geoEvent.GeoPosition);
                    return true;
                }
                return false;
            };

            // 工具提示变更事件映射
            _legacyEventHandlers[typeof(ToolTipChangedEvent)] = (evt) =>
            {
                if (evt is ToolTipChangedEvent tooltipEvent)
                {
                    _legacySystem.Status.OnToolTipChanged?.Invoke(tooltipEvent.ToolTip);
                    return true;
                }
                return false;
            };
        }

        /// <summary>
        /// 初始化属性事件映射
        /// </summary>
        private void InitializePropertyEventHandlers()
        {
            // 属性变更事件映射
            _legacyEventHandlers[typeof(PropertyChangedEvent)] = (evt) =>
            {
                if (evt is PropertyChangedEvent propertyEvent)
                {
                    _legacySystem.Property.OnPropertyChanged?.Invoke(propertyEvent.PropertyName, propertyEvent.NewValue);
                    return true;
                }
                return false;
            };

            // 属性编辑器请求事件映射
            _legacyEventHandlers[typeof(PropertyEditorRequestedEvent)] = (evt) =>
            {
                if (evt is PropertyEditorRequestedEvent editorEvent)
                {
                    _legacySystem.Property.OnPropertyEditorRequested?.Invoke(editorEvent.PropertyType);
                    return true;
                }
                return false;
            };

            // 属性刷新请求事件映射
            _legacyEventHandlers[typeof(PropertiesRefreshRequestedEvent)] = (evt) =>
            {
                if (evt is PropertiesRefreshRequestedEvent)
                {
                    _legacySystem.Property.OnPropertiesRefreshRequested?.Invoke();
                    return true;
                }
                return false;
            };
        }

        /// <summary>
        /// 初始化自定义事件映射
        /// </summary>
        private void InitializeCustomEventHandlers()
        {
            // 自定义事件映射
            _legacyEventHandlers[typeof(CustomEvent)] = (evt) =>
            {
                if (evt is CustomEvent customEvent)
                {
                    _legacySystem.Custom.OnCustomEvent?.Invoke(customEvent.EventName, customEvent.Data);
                    return true;
                }
                return false;
            };
        }

        #region IEventBus实现 - 委托给新系统

        public IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            return _newSystem.Subscribe(handler);
        }

        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            _newSystem.Unsubscribe(handler);
        }

        public void Unsubscribe(IEventSubscription subscription)
        {
            _newSystem.Unsubscribe(subscription);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            return _newSystem.Subscribe(handler, filter);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            return _newSystem.Subscribe(handler, priority);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent
        {
            return _newSystem.Subscribe(handler, filter, priority);
        }

        public void Publish<T>(T eventData) where T : IEvent
        {
            // 首先尝试通过新系统发布
            _newSystem.Publish(eventData);

            // 然后检查是否需要通过旧系统发布
            if (IsLegacyEventType<T>())
            {
                PublishToLegacySystem(eventData);
            }
        }

        public Task PublishAsync<T>(T eventData) where T : IEvent
        {
            return _newSystem.PublishAsync(eventData);
        }

        public void PublishDelayed<T>(T eventData, float delay) where T : IEvent
        {
            _newSystem.PublishDelayed(eventData, delay);
        }

        public void PublishBatch<T>(IEnumerable<T> events) where T : IEvent
        {
            _newSystem.PublishBatch(events);
        }

        public int GetSubscriberCount<T>() where T : IEvent
        {
            return _newSystem.GetSubscriberCount<T>();
        }

        public bool HasSubscribers<T>() where T : IEvent
        {
            return _newSystem.HasSubscribers<T>();
        }

        public void ClearAllSubscriptions()
        {
            _newSystem.ClearAllSubscriptions();
        }

        public void ClearSubscriptions<T>() where T : IEvent
        {
            _newSystem.ClearSubscriptions<T>();
        }

        public IEventStatistics GetStatistics()
        {
            return _newSystem.GetStatistics();
        }

        #endregion

        #region 兼容性方法

        /// <summary>
        /// 检查是否为旧系统事件类型
        /// </summary>
        private bool IsLegacyEventType<T>() where T : IEvent
        {
            return _legacyEventHandlers.ContainsKey(typeof(T));
        }

        /// <summary>
        /// 向旧系统发布事件
        /// </summary>
        private void PublishToLegacySystem<T>(T eventData) where T : IEvent
        {
            Type eventType = typeof(T);

            if (_legacyEventHandlers.TryGetValue(eventType, out Func<IEvent, bool> handler))
            {
                try
                {
                    bool handled = handler(eventData);

                    if (_enableDebugLogging)
                    {
                        Debug.Log($"UIEventSystemAdapter: 向旧系统发布事件 {eventType.Name}，处理结果: {handled}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"UIEventSystemAdapter: 向旧系统发布事件 {eventType.Name} 时发生错误: {ex.Message}");
                }
            }
        }

        #endregion

        public void Dispose()
        {
            _newSystem?.Dispose();
        }
    }
}
