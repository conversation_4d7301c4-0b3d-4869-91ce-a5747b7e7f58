using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件调度器
    /// 支持延迟发布、批量发布等功能
    /// </summary>
    public class EventScheduler : IDisposable
    {
        private readonly List<ScheduledEvent> _scheduledEvents;
        private readonly List<BatchEvent> _batchEvents;
        private readonly object _lockObject = new object();
        private readonly bool _enableDebugLogging;
        
        // 批处理设置
        private readonly float _batchProcessInterval;
        private float _lastBatchProcessTime;
        
        public EventScheduler(float batchProcessInterval = 0.016f, bool enableDebugLogging = false)
        {
            _scheduledEvents = new List<ScheduledEvent>();
            _batchEvents = new List<BatchEvent>();
            _batchProcessInterval = batchProcessInterval;
            _enableDebugLogging = enableDebugLogging;
            _lastBatchProcessTime = Time.unscaledTime;
        }
        
        /// <summary>
        /// 调度延迟事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <param name="delay">延迟时间（秒）</param>
        /// <param name="publishCallback">发布回调</param>
        public void ScheduleEvent<T>(T eventData, float delay, Action<T> publishCallback) where T : IEvent
        {
            if (eventData == null || publishCallback == null) return;
            
            var scheduledEvent = new ScheduledEvent
            {
                EventData = eventData,
                ScheduledTime = Time.unscaledTime + delay,
                PublishCallback = () => publishCallback(eventData)
            };
            
            lock (_lockObject)
            {
                _scheduledEvents.Add(scheduledEvent);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventScheduler: 调度事件 {typeof(T).Name}，延迟 {delay} 秒");
                }
            }
        }
        
        /// <summary>
        /// 调度批量事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="events">事件集合</param>
        /// <param name="publishCallback">发布回调</param>
        public void ScheduleBatch<T>(IEnumerable<T> events, Action<T> publishCallback) where T : IEvent
        {
            if (events == null || publishCallback == null) return;
            
            var eventList = events.ToList();
            if (eventList.Count == 0) return;
            
            var batchEvent = new BatchEvent
            {
                Events = eventList.Cast<IEvent>().ToList(),
                PublishCallback = () =>
                {
                    foreach (T evt in eventList)
                    {
                        publishCallback(evt);
                    }
                }
            };
            
            lock (_lockObject)
            {
                _batchEvents.Add(batchEvent);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventScheduler: 调度批量事件 {typeof(T).Name}，数量: {eventList.Count}");
                }
            }
        }
        
        /// <summary>
        /// 处理调度的事件
        /// 应该在主线程的Update中调用
        /// </summary>
        public void ProcessScheduledEvents()
        {
            float currentTime = Time.unscaledTime;
            
            // 处理延迟事件
            ProcessDelayedEvents(currentTime);
            
            // 处理批量事件
            if (currentTime - _lastBatchProcessTime >= _batchProcessInterval)
            {
                ProcessBatchEvents();
                _lastBatchProcessTime = currentTime;
            }
        }
        
        private void ProcessDelayedEvents(float currentTime)
        {
            List<ScheduledEvent> eventsToProcess = null;
            
            lock (_lockObject)
            {
                if (_scheduledEvents.Count > 0)
                {
                    eventsToProcess = _scheduledEvents
                        .Where(e => e.ScheduledTime <= currentTime)
                        .ToList();
                    
                    foreach (var evt in eventsToProcess)
                    {
                        _scheduledEvents.Remove(evt);
                    }
                }
            }
            
            if (eventsToProcess != null && eventsToProcess.Count > 0)
            {
                foreach (var evt in eventsToProcess)
                {
                    try
                    {
                        evt.PublishCallback?.Invoke();
                        
                        if (_enableDebugLogging)
                        {
                            Debug.Log($"EventScheduler: 处理延迟事件 {evt.EventData.GetType().Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventScheduler: 处理延迟事件时发生错误: {ex.Message}");
                    }
                }
            }
        }
        
        private void ProcessBatchEvents()
        {
            List<BatchEvent> batchesToProcess = null;
            
            lock (_lockObject)
            {
                if (_batchEvents.Count > 0)
                {
                    batchesToProcess = new List<BatchEvent>(_batchEvents);
                    _batchEvents.Clear();
                }
            }
            
            if (batchesToProcess != null && batchesToProcess.Count > 0)
            {
                foreach (var batch in batchesToProcess)
                {
                    try
                    {
                        batch.PublishCallback?.Invoke();
                        
                        if (_enableDebugLogging)
                        {
                            Debug.Log($"EventScheduler: 处理批量事件，数量: {batch.Events.Count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventScheduler: 处理批量事件时发生错误: {ex.Message}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 取消指定类型的所有调度事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void CancelScheduledEvents<T>() where T : IEvent
        {
            lock (_lockObject)
            {
                int removedCount = _scheduledEvents.RemoveAll(e => e.EventData is T);
                
                if (_enableDebugLogging && removedCount > 0)
                {
                    Debug.Log($"EventScheduler: 取消了 {removedCount} 个 {typeof(T).Name} 类型的调度事件");
                }
            }
        }
        
        /// <summary>
        /// 清除所有调度事件
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                int scheduledCount = _scheduledEvents.Count;
                int batchCount = _batchEvents.Count;
                
                _scheduledEvents.Clear();
                _batchEvents.Clear();
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"EventScheduler: 清除了 {scheduledCount} 个延迟事件和 {batchCount} 个批量事件");
                }
            }
        }
        
        /// <summary>
        /// 获取调度器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public EventSchedulerStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                return new EventSchedulerStatistics
                {
                    PendingScheduledEvents = _scheduledEvents.Count,
                    PendingBatchEvents = _batchEvents.Count,
                    BatchProcessInterval = _batchProcessInterval
                };
            }
        }
        
        public void Dispose()
        {
            ClearAll();
        }
        
        /// <summary>
        /// 调度事件数据结构
        /// </summary>
        private class ScheduledEvent
        {
            public IEvent EventData { get; set; }
            public float ScheduledTime { get; set; }
            public Action PublishCallback { get; set; }
        }
        
        /// <summary>
        /// 批量事件数据结构
        /// </summary>
        private class BatchEvent
        {
            public List<IEvent> Events { get; set; }
            public Action PublishCallback { get; set; }
        }
    }
    
    /// <summary>
    /// 事件调度器统计信息
    /// </summary>
    public class EventSchedulerStatistics
    {
        public int PendingScheduledEvents { get; set; }
        public int PendingBatchEvents { get; set; }
        public float BatchProcessInterval { get; set; }
    }
}
