# 现有事件类型分析报告

## 概述

本报告详细分析了当前系统中各UI组件的事件使用情况，为事件系统迁移提供基础数据。

## 1. 现有事件系统架构

### 1.1 UIEventSystem核心结构

UIEventSystem作为中心化的事件管理器，定义了以下事件分类：

#### 工具栏事件 (ToolbarEvents)
- `OnMenuItemClicked: UnityEvent<string>` - 菜单项点击事件
- `OnToolSelected: UnityEvent<string>` - 工具选择事件  
- `OnToolbarVisibilityChanged: UnityEvent<bool>` - 工具栏可见性变更事件

#### 面板事件 (PanelEvents)
- `OnPanelCollapsed: UnityEvent<string, bool>` - 面板折叠事件
- `OnPanelResized: UnityEvent<string, float>` - 面板调整大小事件
- `OnPanelVisibilityChanged: UnityEvent<string, bool>` - 面板可见性变更事件

#### 选择事件 (SelectionEvents)
- `OnObjectSelected: UnityEvent<object>` - 对象选择事件
- `OnMultipleObjectsSelected: UnityEvent<object[]>` - 多对象选择事件
- `OnSelectionCleared: UnityEvent` - 选择清除事件

#### 状态事件 (StatusEvents)
- `OnStatusMessageChanged: UnityEvent<string>` - 状态消息变更事件
- `OnMouseWorldPositionChanged: UnityEvent<Vector3>` - 鼠标世界坐标变更事件
- `OnMouseGeoPositionChanged: UnityEvent<Vector2>` - 鼠标地理坐标变更事件
- `OnToolTipChanged: UnityEvent<string>` - 工具提示变更事件

#### 属性事件 (PropertyEvents)
- `OnPropertyChanged: UnityEvent<string, object>` - 属性变更事件
- `OnPropertyEditorRequested: UnityEvent<Type>` - 属性编辑器请求事件
- `OnPropertiesRefreshRequested: UnityEvent` - 属性刷新请求事件

#### 自定义事件 (CustomEvents)
- `OnCustomEvent: UnityEvent<string, object>` - 自定义事件

### 1.2 静态触发方法

UIEventSystem提供了以下静态方法用于触发事件：
- `TriggerMenuItemClicked(string menuItem)`
- `TriggerToolSelected(string tool)`
- `TriggerCustomEvent(string eventName, object data)`
- `TriggerObjectSelected(object selectedObject)`
- `TriggerStatusMessage(string message)`

## 2. 各组件事件使用分析

### 2.1 Toolbar组件

**文件位置**: `Assets/Scripts/UI/Components/Toolbar.cs`

**事件发布**:
- `UIEventSystem.TriggerToolSelected(buttonConfig.name)` - 工具选择
- `UIEventSystem.TriggerStatusMessage($"选择工具: {buttonConfig.displayName}")` - 状态消息

**事件订阅**: 无直接订阅

**迁移影响**: 高 - 核心工具选择功能

### 2.2 StatusBar组件

**文件位置**: `Assets/Scripts/UI/Components/StatusBar.cs`

**事件订阅**:
- `eventSystem.Toolbar.OnToolSelected.AddListener(OnToolSelected)` - 工具选择监听
- `eventSystem.Status.OnStatusMessageChanged.AddListener(UpdateStatusMessage)` - 状态消息监听
- `eventSystem.Status.OnMouseWorldPositionChanged.AddListener(UpdateWorldPosition)` - 世界坐标监听
- `eventSystem.Status.OnMouseGeoPositionChanged.AddListener(UpdateGeoPosition)` - 地理坐标监听
- `eventSystem.Status.OnToolTipChanged.AddListener(UpdateActionHint)` - 工具提示监听

**事件发布**: 通过ShowNotification方法间接发布状态消息

**迁移影响**: 高 - 状态显示核心功能

### 2.3 MenuBar组件

**文件位置**: `Assets/Scripts/UI/Components/MenuBar/MenuBar.EventHandlers.cs`

**事件发布**:
- `UIEventSystem.TriggerMenuItemClicked(item.name)` - 菜单项点击
- `UIEventSystem.TriggerStatusMessage($"执行菜单项: {item.displayName}")` - 状态消息

**事件订阅**: 无直接订阅

**迁移影响**: 高 - 菜单操作核心功能

### 2.4 LeftPanel组件

**文件位置**: `Assets/Scripts/UI/Components/LeftPanel/LeftPanel.Events.cs`

**事件发布**:
- `UIEventSystem.TriggerStatusMessage("项目数据已刷新")` - 状态消息
- `UIEventSystem.TriggerMenuItemClicked("panel-settings")` - 菜单项点击

**事件订阅**: 无直接订阅

**迁移影响**: 中 - 面板操作功能

### 2.5 其他组件

**UIComponentBase**: 基类提供eventSystem引用，所有UI组件都可能使用事件系统

## 3. 事件流向分析

### 3.1 主要事件流

```
用户操作 → UI组件 → UIEventSystem.Trigger* → UnityEvent → 订阅组件 → 响应处理
```

### 3.2 关键事件路径

1. **工具选择流程**:
   ```
   Toolbar.OnToolButtonClicked → UIEventSystem.TriggerToolSelected → StatusBar.OnToolSelected
   ```

2. **菜单操作流程**:
   ```
   MenuBar.OnMenuItemClicked → UIEventSystem.TriggerMenuItemClicked → [订阅者处理]
   ```

3. **状态消息流程**:
   ```
   [各组件] → UIEventSystem.TriggerStatusMessage → StatusBar.UpdateStatusMessage
   ```

## 4. 迁移影响评估

### 4.1 高影响组件 (需要优先迁移)
- **Toolbar**: 核心工具选择功能
- **StatusBar**: 状态显示和坐标更新
- **MenuBar**: 菜单操作功能

### 4.2 中影响组件
- **LeftPanel**: 面板操作功能
- **其他UI组件**: 使用UIComponentBase的组件

### 4.3 低影响组件
- **属性相关组件**: 目前使用较少
- **自定义事件**: 扩展功能

## 5. 新事件系统映射

### 5.1 已创建的新事件类型

基于分析，以下新事件类型已在`CommonEvents.cs`中定义：

- `MenuItemClickedEvent` ← `OnMenuItemClicked`
- `ToolSelectedEvent` ← `OnToolSelected`
- `ToolbarVisibilityChangedEvent` ← `OnToolbarVisibilityChanged`
- `ObjectSelectedEvent` ← `OnObjectSelected`
- `MultipleObjectsSelectedEvent` ← `OnMultipleObjectsSelected`
- `SelectionClearedEvent` ← `OnSelectionCleared`
- `StatusMessageEvent` ← `OnStatusMessageChanged`
- `MouseWorldPositionChangedEvent` ← `OnMouseWorldPositionChanged`
- `MouseGeoPositionChangedEvent` ← `OnMouseGeoPositionChanged`
- `ToolTipChangedEvent` ← `OnToolTipChanged`
- `PanelEvent` ← 通用面板事件
- `PanelCollapsedEvent` ← `OnPanelCollapsed`
- `PanelResizedEvent` ← `OnPanelResized`
- `PanelVisibilityChangedEvent` ← `OnPanelVisibilityChanged`
- `PropertyChangedEvent` ← `OnPropertyChanged`
- `PropertyEditorRequestedEvent` ← `OnPropertyEditorRequested`
- `PropertiesRefreshRequestedEvent` ← `OnPropertiesRefreshRequested`
- `CustomEvent` ← `OnCustomEvent`

### 5.2 映射完整性

✅ 所有UIEventSystem定义的事件类型都有对应的新事件类型
✅ UIEventSystemAdapter已配置相应的映射处理
✅ 兼容性层已实现，支持渐进式迁移

## 6. 推荐迁移顺序

### 阶段1: 核心UI组件 (高优先级)
1. **Toolbar** - 3天
2. **StatusBar** - 2天  
3. **MenuBar** - 5天
4. **LeftPanel** - 5天

### 阶段2: 输入系统整合
1. **InputEventHandler** - 5天
2. **输入优先级管理** - 3天

### 阶段3: 扩展组件
1. **DaisyTree** - 3天
2. **DaisyInput** - 3天
3. **其他DaisyUI组件** - 4天

## 7. 风险评估

### 7.1 高风险项
- StatusBar的多事件订阅可能导致复杂的迁移过程
- MenuBar的下拉菜单逻辑与事件系统紧密耦合

### 7.2 缓解措施
- 使用UIEventSystemAdapter确保兼容性
- 分阶段迁移，每个组件独立验证
- 保留旧系统作为回退方案

## 8. 结论

现有事件系统分析完成，所有必要的新事件类型已定义，映射关系已建立。系统已准备好进行渐进式迁移，建议按照推荐的迁移顺序执行，以最小化风险并确保功能连续性。
