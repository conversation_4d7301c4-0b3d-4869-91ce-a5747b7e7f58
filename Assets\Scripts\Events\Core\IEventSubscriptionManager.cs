using System;
using System.Collections.Generic;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件订阅管理器接口
    /// 负责管理所有事件的订阅和取消订阅
    /// </summary>
    public interface IEventSubscriptionManager : IDisposable
    {
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 条件订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="filter">过滤条件</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent;
        
        /// <summary>
        /// 优先级订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="priority">优先级</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent;
        
        /// <summary>
        /// 条件和优先级订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="priority">优先级</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent;
        
        /// <summary>
        /// 取消订阅
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void Unsubscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 通过订阅句柄取消订阅
        /// </summary>
        /// <param name="subscription">订阅句柄</param>
        void Unsubscribe(IEventSubscription subscription);
        
        /// <summary>
        /// 获取指定事件类型的所有订阅
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>订阅列表</returns>
        IEnumerable<IEventSubscription> GetSubscriptions<T>() where T : IEvent;
        
        /// <summary>
        /// 获取指定事件类型的订阅者数量
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>订阅者数量</returns>
        int GetSubscriberCount<T>() where T : IEvent;
        
        /// <summary>
        /// 检查是否有指定事件类型的订阅者
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>是否有订阅者</returns>
        bool HasSubscribers<T>() where T : IEvent;
        
        /// <summary>
        /// 清除所有订阅
        /// </summary>
        void ClearAllSubscriptions();
        
        /// <summary>
        /// 清除指定事件类型的所有订阅
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        void ClearSubscriptions<T>() where T : IEvent;
        
        /// <summary>
        /// 清理失效的订阅
        /// </summary>
        /// <returns>清理的订阅数量</returns>
        int CleanupInvalidSubscriptions();
    }
}