using System;
using System.Collections.Generic;
using System.Reflection;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 迁移验证结果
    /// </summary>
    public class MigrationValidationResult
    {
        public bool IsValid { get; set; }
        public DateTime ValidationTime { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> ValidatedEventTypes { get; set; } = new List<string>();
        public List<string> ComponentsUsingLegacyEvents { get; set; } = new List<string>();
        public List<string> ComponentsWithUnityEvents { get; set; } = new List<string>();
        public bool AdapterValidated { get; set; }

        /// <summary>
        /// 获取验证摘要
        /// </summary>
        public string GetSummary()
        {
            return $@"
迁移验证结果摘要:
- 验证状态: {(IsValid ? "通过" : "失败")}
- 验证时间: {ValidationTime:yyyy-MM-dd HH:mm:ss}
- 错误数量: {Errors.Count}
- 警告数量: {Warnings.Count}
- 已验证事件类型: {ValidatedEventTypes.Count}
- 使用旧事件系统的组件: {ComponentsUsingLegacyEvents.Count}
- 包含UnityEvent的组件: {ComponentsWithUnityEvents.Count}
- 适配器验证: {(AdapterValidated ? "通过" : "失败")}
";
        }
    }

    /// <summary>
    /// 组件迁移结果
    /// </summary>
    public class ComponentMigrationResult
    {
        public string ComponentName { get; set; }
        public Type ComponentType { get; set; }
        public bool IsSuccessful { get; set; }
        public DateTime MigrationTime { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<UnityEventInfo> UnityEvents { get; set; } = new List<UnityEventInfo>();
        public List<MigrationSuggestion> MigrationSuggestions { get; set; } = new List<MigrationSuggestion>();

        /// <summary>
        /// 获取迁移摘要
        /// </summary>
        public string GetSummary()
        {
            return $@"
组件迁移结果摘要:
- 组件名称: {ComponentName}
- 迁移状态: {(IsSuccessful ? "成功" : "失败")}
- 迁移时间: {MigrationTime:yyyy-MM-dd HH:mm:ss}
- 错误数量: {Errors.Count}
- 警告数量: {Warnings.Count}
- 发现的UnityEvent: {UnityEvents.Count}
- 迁移建议: {MigrationSuggestions.Count}
";
        }
    }

    /// <summary>
    /// UnityEvent信息
    /// </summary>
    public class UnityEventInfo
    {
        public string FieldName { get; set; }
        public Type FieldType { get; set; }
        public object EventValue { get; set; }
        public int ListenerCount { get; set; }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        public string GetDescription()
        {
            return $"字段: {FieldName}, 类型: {FieldType?.Name}, 监听器数量: {ListenerCount}";
        }
    }

    /// <summary>
    /// 迁移建议
    /// </summary>
    public class MigrationSuggestion
    {
        public string OriginalEventName { get; set; }
        public string OriginalEventType { get; set; }
        public string RecommendedEventType { get; set; }
        public string MigrationCode { get; set; }
        public MigrationPriority Priority { get; set; } = MigrationPriority.Medium;
        public string Description { get; set; }
        public List<string> RequiredSteps { get; set; } = new List<string>();

        /// <summary>
        /// 获取建议摘要
        /// </summary>
        public string GetSummary()
        {
            return $@"
迁移建议:
- 原事件: {OriginalEventName} ({OriginalEventType})
- 推荐事件类型: {RecommendedEventType}
- 优先级: {Priority}
- 描述: {Description}
- 所需步骤: {RequiredSteps.Count}
";
        }
    }

    /// <summary>
    /// 迁移优先级
    /// </summary>
    public enum MigrationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 事件类型映射配置
    /// </summary>
    public class EventTypeMappingConfig
    {
        public Dictionary<string, string> UnityEventToNewEventMapping { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, string> LegacyEventToNewEventMapping { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, MigrationRule> MigrationRules { get; set; } = new Dictionary<string, MigrationRule>();

        /// <summary>
        /// 添加UnityEvent映射
        /// </summary>
        public void AddUnityEventMapping(string unityEventName, string newEventType)
        {
            UnityEventToNewEventMapping[unityEventName] = newEventType;
        }

        /// <summary>
        /// 添加旧事件映射
        /// </summary>
        public void AddLegacyEventMapping(string legacyEventName, string newEventType)
        {
            LegacyEventToNewEventMapping[legacyEventName] = newEventType;
        }

        /// <summary>
        /// 添加迁移规则
        /// </summary>
        public void AddMigrationRule(string eventName, MigrationRule rule)
        {
            MigrationRules[eventName] = rule;
        }

        /// <summary>
        /// 获取新事件类型
        /// </summary>
        public string GetNewEventType(string originalEventName)
        {
            if (UnityEventToNewEventMapping.TryGetValue(originalEventName, out string newType))
            {
                return newType;
            }

            if (LegacyEventToNewEventMapping.TryGetValue(originalEventName, out newType))
            {
                return newType;
            }

            return null;
        }

        /// <summary>
        /// 获取迁移规则
        /// </summary>
        public MigrationRule GetMigrationRule(string eventName)
        {
            return MigrationRules.TryGetValue(eventName, out MigrationRule rule) ? rule : null;
        }
    }

    /// <summary>
    /// 迁移规则
    /// </summary>
    public class MigrationRule
    {
        public string EventName { get; set; }
        public string NewEventType { get; set; }
        public string ConversionCode { get; set; }
        public List<string> RequiredNamespaces { get; set; } = new List<string>();
        public List<string> PrerequisiteSteps { get; set; } = new List<string>();
        public MigrationPriority Priority { get; set; } = MigrationPriority.Medium;
        public bool RequiresManualIntervention { get; set; }
        public string Notes { get; set; }

        /// <summary>
        /// 获取规则描述
        /// </summary>
        public string GetDescription()
        {
            return $@"
迁移规则:
- 事件名称: {EventName}
- 新事件类型: {NewEventType}
- 优先级: {Priority}
- 需要手动干预: {RequiresManualIntervention}
- 前置步骤: {PrerequisiteSteps.Count}
- 备注: {Notes}
";
        }
    }

    /// <summary>
    /// 迁移进度信息
    /// </summary>
    public class MigrationProgress
    {
        public int TotalComponents { get; set; }
        public int MigratedComponents { get; set; }
        public int FailedComponents { get; set; }
        public int PendingComponents => TotalComponents - MigratedComponents - FailedComponents;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<string> CompletedComponents { get; set; } = new List<string>();
        public List<string> FailedComponentsWithErrors { get; set; } = new List<string>();

        /// <summary>
        /// 获取完成百分比
        /// </summary>
        public float GetCompletionPercentage()
        {
            if (TotalComponents == 0) return 0f;
            return (float)MigratedComponents / TotalComponents * 100f;
        }

        /// <summary>
        /// 获取进度摘要
        /// </summary>
        public string GetSummary()
        {
            var duration = EndTime.HasValue ? EndTime.Value - StartTime : DateTime.UtcNow - StartTime;

            return $@"
迁移进度摘要:
- 总组件数: {TotalComponents}
- 已迁移: {MigratedComponents}
- 失败: {FailedComponents}
- 待处理: {PendingComponents}
- 完成百分比: {GetCompletionPercentage():F1}%
- 开始时间: {StartTime:yyyy-MM-dd HH:mm:ss}
- 持续时间: {duration.TotalMinutes:F1} 分钟
- 状态: {(EndTime.HasValue ? "已完成" : "进行中")}
";
        }
    }

    /// <summary>
    /// UnityEvent映射配置
    /// </summary>
    [System.Serializable]
    public class UnityEventMapping
    {
        public string UnityEventName;
        public string NewEventType;
        public string ConversionCode;
        public MigrationPriority Priority = MigrationPriority.Medium;
        public bool RequiresManualIntervention;
        public string Notes;
    }

    /// <summary>
    /// 旧事件映射配置
    /// </summary>
    [System.Serializable]
    public class LegacyEventMapping
    {
        public string LegacyEventName;
        public string NewEventType;
        public string ConversionCode;
        public MigrationPriority Priority = MigrationPriority.Medium;
        public bool RequiresManualIntervention;
        public string Notes;
    }

    /// <summary>
    /// 组件映射规则
    /// </summary>
    [System.Serializable]
    public class ComponentMappingRule
    {
        public string ComponentName;
        public MigrationPriority Priority = MigrationPriority.Medium;
        public bool RequiresManualIntervention;
        public int EstimatedMigrationTime; // 预估迁移时间（分钟）
        public string Notes;
        public string[] PrerequisiteComponents; // 前置依赖组件
        public string[] ConflictingComponents; // 冲突组件
    }
}
