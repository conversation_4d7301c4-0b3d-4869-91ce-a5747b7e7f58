using UnityEngine.Events;
using NUnit.Framework;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 事件迁移验证测试
    /// 用于验证新旧事件系统的桥接功能
    /// </summary>
    public class MigrationValidationTests
    {
        private IEventBus _eventBus;
        private bool _eventReceived;
        private string _receivedData;

        [SetUp]
        public void Setup()
        {
            // 创建测试环境
            _eventBus = new EventBus(debugMode: true);
            
            // 重置测试状态
            _eventReceived = false;
            _receivedData = null;
        }

        [TearDown]
        public void TearDown()
        {
            _eventBus?.Dispose();
        }

        [Test]
        public void TestEventBusBasicFunctionality()
        {
            // Arrange
            _eventBus.Subscribe<TestMigrationEvent>(evt => {
                _eventReceived = true;
                _receivedData = evt.Data;
            });

            // Act
            _eventBus.Publish(new TestMigrationEvent("TestData"));

            // Assert
            Assert.IsTrue(_eventReceived, "Event should be received");
            Assert.AreEqual("TestData", _receivedData, "Data should be passed correctly");
        }

        [Test]
        public void TestLegacyEventIntegration()
        {
            // Arrange
            var legacyEvent = new UnityEvent();
            bool legacyEventReceived = false;
            
            legacyEvent.AddListener(() => legacyEventReceived = true);
            
            _eventBus.Subscribe<TestMigrationEvent>(evt => {
                _eventReceived = true;
                _receivedData = evt.Data;
                // 模拟桥接：触发遗留事件
                legacyEvent.Invoke();
            });

            // Act
            _eventBus.Publish(new TestMigrationEvent("LegacyTestData"));

            // Assert
            Assert.IsTrue(_eventReceived, "New event should be received");
            Assert.IsTrue(legacyEventReceived, "Legacy event should be triggered through bridge");
            Assert.AreEqual("LegacyTestData", _receivedData, "Data should be passed correctly");
        }

        [Test]
        public void TestEventSubscriptionAndUnsubscription()
        {
            // Arrange
            var subscription = _eventBus.Subscribe<TestMigrationEvent>(evt => {
                _eventReceived = true;
                _receivedData = evt.Data;
            });

            // Act - 发布事件，应该接收到
            _eventBus.Publish(new TestMigrationEvent("TestData1"));
            Assert.IsTrue(_eventReceived, "Event should be received");

            // 取消订阅
            _eventBus.Unsubscribe(subscription);
            _eventReceived = false;

            // Act - 再次发布事件，不应该接收到
            _eventBus.Publish(new TestMigrationEvent("TestData2"));

            // Assert
            Assert.IsFalse(_eventReceived, "Event should not be received after unsubscription");
        }

        [Test]
        public void TestEventWithParameterHandling()
        {
            // Arrange
            var legacyEvent = new UnityEvent<string>();
            string receivedLegacyData = null;
            
            legacyEvent.AddListener(data => {
                receivedLegacyData = data;
            });

            _eventBus.Subscribe<TestMigrationEvent>(evt => {
                _eventReceived = true;
                _receivedData = evt.Data;
                // 模拟桥接：将数据传递给遗留事件
                legacyEvent.Invoke(evt.Data);
            });

            // Act
            _eventBus.Publish(new TestMigrationEvent("ParameterTestData"));

            // Assert
            Assert.IsTrue(_eventReceived, "Event should be received");
            Assert.AreEqual("ParameterTestData", _receivedData, "Event data should be correct");
            Assert.AreEqual("ParameterTestData", receivedLegacyData, "Legacy event data should be correct");
        }

        [Test]
        public void TestMultipleSubscribers()
        {
            // Arrange
            int subscriberCount = 0;
            
            _eventBus.Subscribe<TestMigrationEvent>(evt => subscriberCount++);
            _eventBus.Subscribe<TestMigrationEvent>(evt => subscriberCount++);
            _eventBus.Subscribe<TestMigrationEvent>(evt => subscriberCount++);

            // Act
            _eventBus.Publish(new TestMigrationEvent("MultipleSubscriberTest"));

            // Assert
            Assert.AreEqual(3, subscriberCount, "All subscribers should receive the event");
        }

        [Test]
        public void TestErrorHandlingInSubscriber()
        {
            // Arrange
            bool secondSubscriberReceived = false;
            
            _eventBus.Subscribe<TestMigrationEvent>(evt => {
                throw new System.Exception("Test exception");
            });
            
            _eventBus.Subscribe<TestMigrationEvent>(evt => {
                secondSubscriberReceived = true;
            });

            // Act & Assert
            Assert.DoesNotThrow(() => {
                _eventBus.Publish(new TestMigrationEvent("ErrorTestData"));
            }, "EventBus should handle exceptions gracefully");
            
            // 验证第二个订阅者是否收到事件（取决于错误处理实现）
            // 在有些实现中，异常可能会阻止后续订阅者接收事件
            // 这里我们不做强制断言，因为这取决于具体的错误处理策略
            Assert.That(secondSubscriberReceived, Is.True.Or.False, 
                "Second subscriber reception depends on error handling implementation");
        }

        /// <summary>
        /// 测试用事件类型
        /// </summary>
        private class TestMigrationEvent : EventBase
        {
            public string Data { get; }
            
            public TestMigrationEvent(string data) : base("TestSource")
            {
                Data = data;
            }
        }
    }
}